#!/usr/bin/env bash

cd "$(dirname "$0")"

OUT_DIR="./src/modules/protocol"
OUT_DIR_ABS=$(pwd)/${OUT_DIR}
PROTO_DIR="./proto"
BIN_DIR="./node_modules/.bin"
BIN_DIR_ABS=$(pwd)/${BIN_DIR}
CONNECTRPC_DIR="./proto/connectrpc"

${BIN_DIR}/pbjs -t json -o ${OUT_DIR}/bf_proto.json ${PROTO_DIR}/*.proto ${PROTO_DIR}/**/*.proto
cd ${CONNECTRPC_DIR} && npx buf generate
cd ${OUT_DIR_ABS}/gen
${BIN_DIR_ABS}/prettier --write .
