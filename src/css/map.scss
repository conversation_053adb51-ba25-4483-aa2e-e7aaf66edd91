.maplibregl-popup[class*="maplibregl-popup-anchor-"] {
  .maplibregl-popup-tip {
    display: none;
  }

  .maplibregl-popup-content {
    width: 360px;
    height: auto;
    min-height: 150px;
    background-image: url('@/assets/images/dispatch/map/map-popup.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    background-color: transparent;
    box-shadow: unset;
    display: flex;
    flex-direction: column;

    .popup-wrapper {
      margin-top: 28px;
      color: #fff;
      background: linear-gradient(to bottom, #1482CD70 0%, #1685CD29 100%);
      padding: 10px;
      margin-left: 4px;
      flex: 1;

      .popup_title {
        font-family: 'YouSheBiaoTiHei';
        font-size: 18px;
        position: absolute;
        top: -26px;
        left: 0;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .close_icon {
        width: 32px;
        height: 32px;
        position: absolute;
        top: -44px;
        right: 0;
        background-image: url('@/components/bfDialog/images/close.png');
        background-size: 100% 100%;
        cursor: pointer;
      }

      .popup-body {
        .popup-item-label {
          font-family: 'AlibabaPuHuiTi2';
          font-size: 12px;
        }

        .popup-item-value {
          font-family: 'YouSheBiaoTiHei';
          font-size: 14px;
          color: #FF811D;
        }

        .popup-active-device-list,
        .popup-active-device-item {
          background-color: transparent;
        }

        .popup-active-device-item {
          border-top: 1px solid #fff;
        }
      }

      .popup-footer {
        text-align: center;
      }
    }

    /* 设备操作按钮样式 - 与 popup-wrapper 同级 */
    .popup-device-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 10px;
      color: #fff;
      margin-left: 4px;
      width: 100%;
      box-sizing: border-box;

      .device-action-row {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;

        .device-action-btn {
          flex: 1;
          padding: 0 8px;
          font-size: 14px;
          text-align: center;
          color: #fff;
          text-shadow: 0px 1px 3px rgba(0, 131, 255, 0.501961);
          height: 42px;
          line-height: 42px;
          border: none;
          cursor: pointer;
          font-family: 'AlibabaPuHuiTi2';
          font-weight: 500;

          /* 文本超出显示省略号 */
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          &:active {
            opacity: 0.5;
          }
        }
      }

      .device-action-row:first-child {
        align-items: center;

        .device-action-btn.left-btn {
          background-image: url('@/assets/images/dispatch/map/map-popup-left-btn.svg');
          background-size: 100% 100%;
        }


        .device-action-btn.right-btn {
          background-image: url('@/assets/images/dispatch/map/map-popup-right-btn.svg');
          background-size: 100% 100%;
        }
      }

      .device-action-row:last-child {
        align-items: flex-end;

        .device-action-btn.left-btn {
          background-image: url('@/assets/images/dispatch/contact_card/left_btn_bg.svg');
          background-size: 100% 100%;
        }

        .device-action-btn.center-btn {
          background-image: url('@/assets/images/dispatch/contact_card/center_btn_bg.svg');
          background-size: 100% 100%;
          height: 34px;
          line-height: 34px;
        }

        .device-action-btn.right-btn {
          background-image: url('@/assets/images/dispatch/contact_card/right_btn_bg.svg');
          background-size: 100% 100%;
        }

      }
    }

    p {
      text-align: left;
    }
  }
}

// 地图控件容器
.maplibregl-control-container {
  .maplibregl-ctrl-top-right {

    /* 自定义地图控件容器样式 */
    .custom-map-controls.maplibregl-ctrl-group {
      display: flex;
      flex-direction: column;
      gap: 1px;
      background-color: transparent;


      /* 地图控件按钮样式 */
      .zoom_in_btn {
        background: url(@/assets/images/dispatch/map/zoom-in.svg) no-repeat center center;
        background-size: 42px 42px;
      }

      .zoom_out_btn {
        background: url(@/assets/images/dispatch/map/zoom-out.svg) no-repeat center center;
        background-size: 42px 42px;
      }

      .satellite_btn {
        background: url(@/assets/images/dispatch/map/satellite.svg) no-repeat center center;
        background-size: 42px 42px;
      }

      .streets_btn {
        background: url(@/assets/images/dispatch/map/streets.svg) no-repeat center center;
        background-size: 42px 42px;
      }

      .location_btn {
        background: url(@/assets/images/dispatch/map/to-north.svg) no-repeat center center;
        background-size: 42px 42px;
      }

      .distances_btn {
        background: url(@/assets/images/dispatch/map/ruler.svg) no-repeat center center;
        background-size: 42px 42px;
      }

      .quitMap_btn {
        background: url(@/assets/images/dispatch/map/over_trace.svg) no-repeat center center;
        background-size: 42px 42px;
      }

      button {
        width: 42px;
        height: 42px;
        border: none;
        cursor: pointer;
        background-color: transparent;
        margin: 0;
        padding: 0;
      }

      &:not(:empty) {
        box-shadow: unset;
      }
    }
  }
}

.maplibregl-canvas-container {
  .markerContainer {
    .device_status {
      width: 28px;
      height: 34px;
      margin-right: 2px;
      position: relative;
      display: inline-block;
      vertical-align: sub;

      &_none {
        background-image: none;
      }

      &_yellow {
        background: url('@/assets/images/dispatch/map/yellow_cir.svg') no-repeat center center;
        background-size: cover;
      }

      &_light_gray {
        background: url('@/assets/images/dispatch/map/light_gray_cir.svg') no-repeat center center;
        background-size: cover;
      }

      &_gray {
        background: url('@/assets/images/dispatch/map/gray_cir.svg') no-repeat center center;
        background-size: cover;
      }

      &_green {
        background: url('@/assets/images/dispatch/map/green_cir.svg') no-repeat center center;
        background-size: cover;
      }

      &_red {
        background: url('@/assets/images/dispatch/map/red.svg') no-repeat center center;
        background-size: cover;
      }

      &_other_red {
        position: relative;

        &:after {
          content: '';
          display: block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: absolute;
          top: 4px;
          right: 4px;
          z-index: 10;
          background-color: red;
        }
      }

      &_icon_th {
        background: url('@/assets/images/dispatch/map/th.svg') no-repeat center center;
        background-size: cover;
      }

      &_emergency_green {
        background: url('@/assets/images/dispatch/map/device_emergency_green.gif') no-repeat center center;
        background-size: cover;
      }

      &_emergency_yellow {
        background: url('@/assets/images/dispatch/map/device_emergency_yellow.gif') no-repeat center center;
        background-size: cover;
      }
    }

  }
}