// vue 组件的 <style> 需要设置 lang="scss" scoped
:deep(.data-table-row-item) {
  &:has(.el-select) {
    cursor: text;
  }
  .data-table-row-item__content .el-select {
    position: relative;
    color: #dde6f6;
    .el-select__wrapper {
      background-color: transparent;
      box-shadow: none;
      min-height: auto;
      padding: 0;
      font-size: 13px;
      height: 29px;
      &:not(.is-focused) {
        box-shadow: none;
      }
      .el-select__input {
        color: #dde6f6;

        width: 123px;
      }
      .el-select__placeholder {
        color: #dde6f6;
      }
      .el-icon.el-select__icon {
        color: #7fb5e5;
      }
    }
  }
}
