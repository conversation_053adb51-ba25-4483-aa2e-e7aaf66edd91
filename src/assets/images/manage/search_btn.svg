<svg viewBox="0 0 164 49" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_8)">
<g clip-path="url(#clip1_0_8)">
<g filter="url(#filter0_i_0_8)">
<path d="M13.0573 0H150.943L164 23.9787L150.943 47.9574H13.0573L0 23.9787L13.0573 0V0Z" fill="#FDA318"/>
</g>
<mask id="mask0_0_8" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="164" height="48">
<path d="M13.0573 0H150.943L164 23.9787L150.943 47.9574H13.0573L0 23.9787L13.0573 0V0Z" fill="#FDA216"/>
</mask>
<g mask="url(#mask0_0_8)">
<g opacity="0.5" filter="url(#filter1_f_0_8)">
<ellipse cx="81.9998" cy="46.915" rx="40.2166" ry="27.1064" fill="#5295F9" fill-opacity="0.729412"/>
</g>
<g opacity="0.6" filter="url(#filter2_f_0_8)">
<ellipse cx="82" cy="46.915" rx="18.2803" ry="27.1064" fill="#B37E43"/>
</g>
<g filter="url(#filter3_f_0_8)">
<ellipse cx="82" cy="46.9151" rx="12.0127" ry="7.29787" fill="#BB8445"/>
</g>
</g>
<path d="M150.634 0.519531L163.407 23.9785L150.634 47.4375H13.3662L0.591797 23.9785L13.3662 0.519531H150.634Z" stroke="url(#paint0_linear_0_8)" stroke-width="1.04"/>
<path d="M150.634 0.519531L163.407 23.9785L150.634 47.4375H13.3662L0.591797 23.9785L13.3662 0.519531H150.634Z" stroke="url(#paint1_linear_0_8)" stroke-width="1.04"/>
<path d="M150.634 0.519531L163.407 23.9785L150.634 47.4375H13.3662L0.591797 23.9785L13.3662 0.519531H150.634Z" stroke="#F5C163" stroke-width="1.04"/>
<rect x="18.8027" y="4.16992" width="127.439" height="1.04255" fill="url(#paint2_linear_0_8)"/>
<rect x="32.2402" y="45.7598" width="100.389" height="3.24" fill="url(#paint3_linear_0_8)"/>
<rect x="37.4688" y="46.8398" width="89.9314" height="1.08" fill="url(#paint4_linear_0_8)"/>
<rect x="39.5601" y="45.7598" width="86.7943" height="1.08" fill="url(#paint5_linear_0_8)"/>
</g>
</g>
<defs>
<filter id="filter0_i_0_8" x="0" y="0" width="164" height="47.957" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.713726 0 0 0 0 0.498039 0 0 0 0 0.27451 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_8"/>
</filter>
<filter id="filter1_f_0_8" x="-4.98724" y="-26.9618" width="173.974" height="147.754" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="23.3852" result="effect1_foregroundBlur_0_8"/>
</filter>
<filter id="filter2_f_0_8" x="32.5394" y="-11.3717" width="98.9211" height="116.573" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15.5901" result="effect1_foregroundBlur_0_8"/>
</filter>
<filter id="filter3_f_0_8" x="49.2004" y="18.8303" width="65.5991" height="56.1694" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.3934" result="effect1_foregroundBlur_0_8"/>
</filter>
<linearGradient id="paint0_linear_0_8" x1="0.522293" y1="43.7992" x2="136.082" y2="43.7992" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0D8FF" stop-opacity="0.01"/>
<stop offset="0.272296" stop-color="#B0D8FF" stop-opacity="0.01"/>
<stop offset="0.618939" stop-color="#B0D8FF" stop-opacity="0.678431"/>
<stop offset="1" stop-color="#B0D8FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint1_linear_0_8" x1="18.7208" y1="0" x2="18.7208" y2="37.0086" gradientUnits="userSpaceOnUse">
<stop stop-color="#BFE0FF" stop-opacity="0.01"/>
<stop offset="0.272322" stop-color="#BFE0FF" stop-opacity="0.01"/>
<stop offset="0.65862" stop-color="#BFE0FF" stop-opacity="0.670588"/>
<stop offset="1" stop-color="#BFE0FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_8" x1="18.8027" y1="5.73357" x2="146.242" y2="5.73357" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.378996" stop-color="#E0B620" stop-opacity="0.647059"/>
<stop offset="0.593285" stop-color="#B98446"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint3_linear_0_8" x1="32.2402" y1="50.6193" x2="132.629" y2="50.6193" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.355012" stop-color="#D9A974"/>
<stop offset="0.517572" stop-color="#FFDCB7"/>
<stop offset="0.659173" stop-color="#DCA86E"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint4_linear_0_8" x1="37.4688" y1="48.4597" x2="127.4" y2="48.4597" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.355012" stop-color="#D9A974"/>
<stop offset="0.517572" stop-color="#FFDCB7"/>
<stop offset="0.659173" stop-color="#DCA86E"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint5_linear_0_8" x1="39.5601" y1="47.3796" x2="126.354" y2="47.3796" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.355012" stop-color="#D9A974"/>
<stop offset="0.517572" stop-color="#FFDCB7"/>
<stop offset="0.659173" stop-color="#DCA86E"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<clipPath id="clip0_0_8">
<rect width="164" height="49" fill="white"/>
</clipPath>
<clipPath id="clip1_0_8">
<rect width="164" height="49" fill="white"/>
</clipPath>
</defs>
</svg>
