<svg width="396" height="261" viewBox="0 0 396 261" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2030_291)">
<g filter="url(#filter0_i_2030_291)">
<path d="M140.353 4.4668L145.044 24H387L396 54.4229V264H12V187.697H1V3H140.353V4.4668Z" fill="#010F23" fill-opacity="0.701961"/>
</g>
<path d="M145.054 24H387L395 55.2559V263L386 263.5H395V264H12V186.609H1V3H140L145.054 24Z" fill="url(#paint0_radial_2030_291)"/>
<path d="M145.054 24H387L395 55.2559V263L386 263.5H395V264H12V186.609H1V3H140L145.054 24Z" fill="url(#paint1_radial_2030_291)"/>
<mask id="path-3-inside-1_2030_291" fill="white">
<path d="M140.353 4.4668L145.044 24H387L396 54.4229V264H12V187.697H1V3H140.353V4.4668Z"/>
</mask>
<path d="M140.353 4.4668H138.353V4.70361L138.408 4.93387L140.353 4.4668ZM145.044 24L143.099 24.4671L143.467 26H145.044V24ZM387 24L388.918 23.4326L388.494 22H387V24ZM396 54.4229H398V54.1332L397.918 53.8555L396 54.4229ZM396 264V266H398V264H396ZM12 264H10V266H12V264ZM12 187.697H14V185.697H12V187.697ZM1 187.697H-1V189.697H1V187.697ZM1 3V1H-1V3H1ZM140.353 3H142.353V1H140.353V3ZM140.353 4.4668L138.408 4.93387L143.099 24.4671L145.044 24L146.989 23.5329L142.297 3.99973L140.353 4.4668ZM145.044 24V26H387V24V22H145.044V24ZM387 24L385.082 24.5674L394.082 54.9902L396 54.4229L397.918 53.8555L388.918 23.4326L387 24ZM396 54.4229H394V264H396H398V54.4229H396ZM396 264V262H12V264V266H396V264ZM12 264H14V187.697H12H10V264H12ZM12 187.697V185.697H1V187.697V189.697H12V187.697ZM1 187.697H3V3H1H-1V187.697H1ZM1 3V5H140.353V3V1H1V3ZM140.353 3H138.353V4.4668H140.353H142.353V3H140.353Z" fill="#0881D7" fill-opacity="0.701961" mask="url(#path-3-inside-1_2030_291)"/>
<g clip-path="url(#clip1_2030_291)">
<g opacity="0.151238" filter="url(#filter1_f_2030_291)">
<path d="M140.176 3.71875H136.676V4.14019L136.776 4.54959L140.176 3.71875ZM145.132 24L141.732 24.8308L142.384 27.5H145.132V24ZM387 24L390.365 23.0374L389.639 20.5H387V24ZM395.499 53.709H398.999V53.2182L398.864 52.7463L395.499 53.709ZM395.499 238V241.5H398.999V238H395.499ZM386 238V234.5H381.536L382.601 238.835L386 238ZM391.276 259.472V262.972H395.741L394.675 258.636L391.276 259.472ZM12 259.472H8.5V262.972H12V259.472ZM12 184.191H15.5V180.691H12V184.191ZM1 184.191H-2.5V187.691H1V184.191ZM1 3V-0.5H-2.5V3H1ZM140.176 3H143.676V-0.5H140.176V3ZM140.176 3.71875L136.776 4.54959L141.732 24.8308L145.132 24L148.532 23.1692L143.576 2.88791L140.176 3.71875ZM145.132 24V27.5H387V24V20.5H145.132V24ZM387 24L383.635 24.9626L392.134 54.6716L395.499 53.709L398.864 52.7463L390.365 23.0374L387 24ZM395.499 53.709H391.999V238H395.499H398.999V53.709H395.499ZM395.499 238V234.5H386V238V241.5H395.499V238ZM386 238L382.601 238.835L387.877 260.307L391.276 259.472L394.675 258.636L389.399 237.165L386 238ZM391.276 259.472V255.972H12V259.472V262.972H391.276V259.472ZM12 259.472H15.5V184.191H12H8.5V259.472H12ZM12 184.191V180.691H1V184.191V187.691H12V184.191ZM1 184.191H4.5V3H1H-2.5V184.191H1ZM1 3V6.5H140.176V3V-0.5H1V3ZM140.176 3H136.676V3.71875H140.176H143.676V3H140.176Z" fill="url(#paint2_radial_2030_291)"/>
<path d="M140.176 3.71875H136.676V4.14019L136.776 4.54959L140.176 3.71875ZM145.132 24L141.732 24.8308L142.384 27.5H145.132V24ZM387 24L390.365 23.0374L389.639 20.5H387V24ZM395.499 53.709H398.999V53.2182L398.864 52.7463L395.499 53.709ZM395.499 238V241.5H398.999V238H395.499ZM386 238V234.5H381.536L382.601 238.835L386 238ZM391.276 259.472V262.972H395.741L394.675 258.636L391.276 259.472ZM12 259.472H8.5V262.972H12V259.472ZM12 184.191H15.5V180.691H12V184.191ZM1 184.191H-2.5V187.691H1V184.191ZM1 3V-0.5H-2.5V3H1ZM140.176 3H143.676V-0.5H140.176V3ZM140.176 3.71875L136.776 4.54959L141.732 24.8308L145.132 24L148.532 23.1692L143.576 2.88791L140.176 3.71875ZM145.132 24V27.5H387V24V20.5H145.132V24ZM387 24L383.635 24.9626L392.134 54.6716L395.499 53.709L398.864 52.7463L390.365 23.0374L387 24ZM395.499 53.709H391.999V238H395.499H398.999V53.709H395.499ZM395.499 238V234.5H386V238V241.5H395.499V238ZM386 238L382.601 238.835L387.877 260.307L391.276 259.472L394.675 258.636L389.399 237.165L386 238ZM391.276 259.472V255.972H12V259.472V262.972H391.276V259.472ZM12 259.472H15.5V184.191H12H8.5V259.472H12ZM12 184.191V180.691H1V184.191V187.691H12V184.191ZM1 184.191H4.5V3H1H-2.5V184.191H1ZM1 3V6.5H140.176V3V-0.5H1V3ZM140.176 3H136.676V3.71875H140.176H143.676V3H140.176Z" fill="url(#paint3_radial_2030_291)"/>
<path d="M140.176 3.71875H136.676V4.14019L136.776 4.54959L140.176 3.71875ZM145.132 24L141.732 24.8308L142.384 27.5H145.132V24ZM387 24L390.365 23.0374L389.639 20.5H387V24ZM395.499 53.709H398.999V53.2182L398.864 52.7463L395.499 53.709ZM395.499 238V241.5H398.999V238H395.499ZM386 238V234.5H381.536L382.601 238.835L386 238ZM391.276 259.472V262.972H395.741L394.675 258.636L391.276 259.472ZM12 259.472H8.5V262.972H12V259.472ZM12 184.191H15.5V180.691H12V184.191ZM1 184.191H-2.5V187.691H1V184.191ZM1 3V-0.5H-2.5V3H1ZM140.176 3H143.676V-0.5H140.176V3ZM140.176 3.71875L136.776 4.54959L141.732 24.8308L145.132 24L148.532 23.1692L143.576 2.88791L140.176 3.71875ZM145.132 24V27.5H387V24V20.5H145.132V24ZM387 24L383.635 24.9626L392.134 54.6716L395.499 53.709L398.864 52.7463L390.365 23.0374L387 24ZM395.499 53.709H391.999V238H395.499H398.999V53.709H395.499ZM395.499 238V234.5H386V238V241.5H395.499V238ZM386 238L382.601 238.835L387.877 260.307L391.276 259.472L394.675 258.636L389.399 237.165L386 238ZM391.276 259.472V255.972H12V259.472V262.972H391.276V259.472ZM12 259.472H15.5V184.191H12H8.5V259.472H12ZM12 184.191V180.691H1V184.191V187.691H12V184.191ZM1 184.191H4.5V3H1H-2.5V184.191H1ZM1 3V6.5H140.176V3V-0.5H1V3ZM140.176 3H136.676V3.71875H140.176H143.676V3H140.176Z" fill="url(#paint4_radial_2030_291)"/>
</g>
<mask id="path-6-inside-2_2030_291" fill="white">
<path d="M140.529 5.15723L145.151 24H387L396.5 53.708V261H12V184.191H1V3H140.529V5.15723Z"/>
</mask>
<path d="M140.529 5.15723H138.529V5.39894L138.587 5.63369L140.529 5.15723ZM145.151 24L143.209 24.4765L143.583 26H145.151V24ZM387 24L388.905 23.3908L388.46 22H387V24ZM396.5 53.708H398.5V53.396L398.405 53.0988L396.5 53.708ZM396.5 261V263H398.5V261H396.5ZM12 261H10V263H12V261ZM12 184.191H14V182.191H12V184.191ZM1 184.191H-1V186.191H1V184.191ZM1 3V1H-1V3H1ZM140.529 3H142.529V1H140.529V3ZM140.529 5.15723L138.587 5.63369L143.209 24.4765L145.151 24L147.094 23.5235L142.472 4.68076L140.529 5.15723ZM145.151 24V26H387V24V22H145.151V24ZM387 24L385.095 24.6092L394.595 54.3172L396.5 53.708L398.405 53.0988L388.905 23.3908L387 24ZM396.5 53.708H394.5V261H396.5H398.5V53.708H396.5ZM396.5 261V259H12V261V263H396.5V261ZM12 261H14V184.191H12H10V261H12ZM12 184.191V182.191H1V184.191V186.191H12V184.191ZM1 184.191H3V3H1H-1V184.191H1ZM1 3V5H140.529V3V1H1V3ZM140.529 3H138.529V5.15723H140.529H142.529V3H140.529Z" fill="url(#paint5_radial_2030_291)" mask="url(#path-6-inside-2_2030_291)"/>
<path d="M140.529 5.15723H138.529V5.39894L138.587 5.63369L140.529 5.15723ZM145.151 24L143.209 24.4765L143.583 26H145.151V24ZM387 24L388.905 23.3908L388.46 22H387V24ZM396.5 53.708H398.5V53.396L398.405 53.0988L396.5 53.708ZM396.5 261V263H398.5V261H396.5ZM12 261H10V263H12V261ZM12 184.191H14V182.191H12V184.191ZM1 184.191H-1V186.191H1V184.191ZM1 3V1H-1V3H1ZM140.529 3H142.529V1H140.529V3ZM140.529 5.15723L138.587 5.63369L143.209 24.4765L145.151 24L147.094 23.5235L142.472 4.68076L140.529 5.15723ZM145.151 24V26H387V24V22H145.151V24ZM387 24L385.095 24.6092L394.595 54.3172L396.5 53.708L398.405 53.0988L388.905 23.3908L387 24ZM396.5 53.708H394.5V261H396.5H398.5V53.708H396.5ZM396.5 261V259H12V261V263H396.5V261ZM12 261H14V184.191H12H10V261H12ZM12 184.191V182.191H1V184.191V186.191H12V184.191ZM1 184.191H3V3H1H-1V184.191H1ZM1 3V5H140.529V3V1H1V3ZM140.529 3H138.529V5.15723H140.529H142.529V3H140.529Z" fill="url(#paint6_radial_2030_291)" mask="url(#path-6-inside-2_2030_291)"/>
<path d="M140.529 5.15723H138.529V5.39894L138.587 5.63369L140.529 5.15723ZM145.151 24L143.209 24.4765L143.583 26H145.151V24ZM387 24L388.905 23.3908L388.46 22H387V24ZM396.5 53.708H398.5V53.396L398.405 53.0988L396.5 53.708ZM396.5 261V263H398.5V261H396.5ZM12 261H10V263H12V261ZM12 184.191H14V182.191H12V184.191ZM1 184.191H-1V186.191H1V184.191ZM1 3V1H-1V3H1ZM140.529 3H142.529V1H140.529V3ZM140.529 5.15723L138.587 5.63369L143.209 24.4765L145.151 24L147.094 23.5235L142.472 4.68076L140.529 5.15723ZM145.151 24V26H387V24V22H145.151V24ZM387 24L385.095 24.6092L394.595 54.3172L396.5 53.708L398.405 53.0988L388.905 23.3908L387 24ZM396.5 53.708H394.5V261H396.5H398.5V53.708H396.5ZM396.5 261V259H12V261V263H396.5V261ZM12 261H14V184.191H12H10V261H12ZM12 184.191V182.191H1V184.191V186.191H12V184.191ZM1 184.191H3V3H1H-1V184.191H1ZM1 3V5H140.529V3V1H1V3ZM140.529 3H138.529V5.15723H140.529H142.529V3H140.529Z" fill="url(#paint7_radial_2030_291)" mask="url(#path-6-inside-2_2030_291)"/>
</g>
<rect y="77" width="9" height="164.286" fill="url(#paint8_linear_2030_291)"/>
<g filter="url(#filter2_i_2030_291)">
<path d="M0 67L9 77.1276V251.548L0 241.42V67V67Z" fill="#00A7ED" fill-opacity="0.77"/>
<path d="M0 67L9 77.1276V251.548L0 241.42V67V67Z" fill="url(#paint9_linear_2030_291)" fill-opacity="0.27" style="mix-blend-mode:overlay"/>
</g>
<g clip-path="url(#clip2_2030_291)">
<mask id="mask0_2030_291" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="147" y="1" width="232" height="21">
<rect x="147" y="1" width="232" height="20.7215" fill="url(#paint10_linear_2030_291)"/>
</mask>
<g mask="url(#mask0_2030_291)">
</g>
<g clip-path="url(#clip3_2030_291)">
<g filter="url(#filter3_i_2030_291)">
<path d="M204 1H363L379 21.7215H220L204 1V1Z" fill="#0781DF" fill-opacity="0.8"/>
</g>
<g filter="url(#filter4_i_2030_291)">
<path d="M147 2H161L175 21.7215H161L147 2V2Z" fill="#0781DF"/>
</g>
<g filter="url(#filter5_i_2030_291)">
<path d="M166 2H180L194 21.7215H180L166 2V2Z" fill="#0781DF" fill-opacity="0.7"/>
</g>
<g filter="url(#filter6_i_2030_291)">
<path d="M185 2H199L213 21.7215H199L185 2V2Z" fill="#0781DF"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_i_2030_291" x="1" y="3" width="395" height="261" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.184314 0 0 0 0 0.647059 0 0 0 0 1 0 0 0 0.36 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2030_291"/>
</filter>
<filter id="filter1_f_2030_291" x="-6.49747" y="-4.49747" width="409.494" height="271.467" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.99874" result="effect1_foregroundBlur_2030_291"/>
</filter>
<filter id="filter2_i_2030_291" x="0" y="67" width="9" height="184.547" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.184314 0 0 0 0 0.741176 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2030_291"/>
</filter>
<filter id="filter3_i_2030_291" x="204" y="1" width="175" height="20.7217" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.839216 0 0 0 0 0.929412 0 0 0 0 1 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2030_291"/>
</filter>
<filter id="filter4_i_2030_291" x="147" y="2" width="28" height="19.7217" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.839216 0 0 0 0 0.929412 0 0 0 0 1 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2030_291"/>
</filter>
<filter id="filter5_i_2030_291" x="166" y="2" width="28" height="19.7217" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.839216 0 0 0 0 0.929412 0 0 0 0 1 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2030_291"/>
</filter>
<filter id="filter6_i_2030_291" x="185" y="2" width="28" height="19.7217" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.839216 0 0 0 0 0.929412 0 0 0 0 1 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2030_291"/>
</filter>
<radialGradient id="paint0_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(0.999998 11.7323) rotate(90) scale(167.37 826.908)">
<stop stop-color="#0BA2E7" stop-opacity="0.56"/>
<stop offset="1" stop-color="#0BA2E7" stop-opacity="0.01"/>
</radialGradient>
<radialGradient id="paint1_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(198 264) rotate(90) scale(72.2844 134.096)">
<stop stop-color="#09A1E6" stop-opacity="0.14"/>
<stop offset="1" stop-color="#09A1E6" stop-opacity="0.01"/>
</radialGradient>
<radialGradient id="paint2_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(198.249 259.472) scale(225.786 191.884)">
<stop stop-color="#21C0FF" stop-opacity="0.717647"/>
<stop offset="1" stop-color="#21C0FF" stop-opacity="0.01"/>
</radialGradient>
<radialGradient id="paint3_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(395.499 27.3124) rotate(90) scale(232.159 504.073)">
<stop stop-color="#21C0FF" stop-opacity="0.882353"/>
<stop offset="1" stop-color="#21C0FF" stop-opacity="0.01"/>
</radialGradient>
<radialGradient id="paint4_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(46.5576 3.00001) rotate(90) scale(114.888 274.923)">
<stop stop-color="#21C0FF" stop-opacity="0.882353"/>
<stop offset="0.217932" stop-color="#21C0FF" stop-opacity="0.882353"/>
<stop offset="1" stop-color="#21C0FF" stop-opacity="0.01"/>
</radialGradient>
<radialGradient id="paint5_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(198.75 261) scale(226.359 193.027)">
<stop stop-color="#B3E8FF" stop-opacity="0.717647"/>
<stop offset="1" stop-color="#B7EAFF" stop-opacity="0.01"/>
</radialGradient>
<radialGradient id="paint6_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(396.5 27.4572) rotate(90) scale(233.543 505.352)">
<stop stop-color="#B7EAFF" stop-opacity="0.882353"/>
<stop offset="1" stop-color="#B7EAFF" stop-opacity="0.01"/>
</radialGradient>
<radialGradient id="paint7_radial_2030_291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(46.6732 3.00001) rotate(90) scale(115.572 275.621)">
<stop stop-color="#B7EAFF" stop-opacity="0.882353"/>
<stop offset="0.217932" stop-color="#B7EAFF" stop-opacity="0.882353"/>
<stop offset="1" stop-color="#B7EAFF" stop-opacity="0.01"/>
</radialGradient>
<linearGradient id="paint8_linear_2030_291" x1="0" y1="77" x2="0" y2="241.286" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C89BE" stop-opacity="0.01"/>
<stop offset="0.267319" stop-color="#0C89BE" stop-opacity="0.631373"/>
<stop offset="0.398292" stop-color="#0C89BE" stop-opacity="0.945098"/>
<stop offset="0.515581" stop-color="#0D8ABE"/>
<stop offset="0.673921" stop-color="#0D89BD" stop-opacity="0.827451"/>
<stop offset="0.800983" stop-color="#0C89BF" stop-opacity="0.313726"/>
<stop offset="1" stop-color="#0C89BF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint9_linear_2030_291" x1="0" y1="67" x2="0" y2="251.548" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint10_linear_2030_291" x1="154.5" y1="21.9318" x2="379" y2="21.9318" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9D9D9"/>
<stop offset="0.223211" stop-color="#D9D9D9" stop-opacity="0.63"/>
<stop offset="1" stop-color="#D9D9D9" stop-opacity="0.11"/>
</linearGradient>
<clipPath id="clip0_2030_291">
<rect width="396" height="261" fill="white"/>
</clipPath>
<clipPath id="clip1_2030_291">
<rect width="395.5" height="258" fill="white" transform="translate(1 3)"/>
</clipPath>
<clipPath id="clip2_2030_291">
<rect width="232" height="20.7215" fill="white" transform="translate(147 1)"/>
</clipPath>
<clipPath id="clip3_2030_291">
<rect width="232" height="20.7215" fill="white" transform="translate(147 1)"/>
</clipPath>
</defs>
</svg>
