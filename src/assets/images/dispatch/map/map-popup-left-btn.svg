<svg width="172" height="42" viewBox="0 0 172 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_5396)">
<g filter="url(#filter0_i_0_5396)">
<path d="M137 7.42683L166.05 7.58476L171.879 13.2029C171.957 13.2936 172 13.4083 172 13.5268V41.258C172 41.536 171.77 41.7612 171.485 41.7612H6.1151L0.120193 34.7653C0.0425609 34.6747 0 34.5603 0 34.4421V2.00327C0 1.7253 0.2304 1.5 0.514654 1.5H133L137 7.42683Z" fill="#0065B6" fill-opacity="0.176471"/>
</g>
<path d="M0.514648 1.75586H132.864L136.788 7.57031L136.863 7.68164L136.999 7.68262L165.945 7.83984L171.688 13.375C171.724 13.4189 171.744 13.472 171.744 13.5264V41.2578C171.744 41.389 171.634 41.5049 171.485 41.5049H6.23242L0.314453 34.5986C0.27629 34.554 0.255922 34.4987 0.255859 34.4424V2.00293C0.256047 1.87186 0.366478 1.75586 0.514648 1.75586Z" stroke="url(#paint0_linear_0_5396)" stroke-width="0.512"/>
<g filter="url(#filter1_i_0_5396)">
<path d="M171.931 11.6662V7.65161H168.084L171.931 11.6662V11.6662Z" fill="#008EFF"/>
</g>
<g filter="url(#filter2_i_0_5396)">
<path d="M0.000447273 38.5003L0.000447273 42H3.3457L0.000447273 38.5003V38.5003Z" fill="#008EFF"/>
</g>
<path d="M0 0.494118H133" stroke="url(#paint1_linear_0_5396)" stroke-width="0.512"/>
<path d="M136.744 7.76871L167 7.49498" stroke="url(#paint2_linear_0_5396)" stroke-width="0.512"/>
</g>
<defs>
<filter id="filter0_i_0_5396" x="0" y="-1.5" width="172" height="43.2612" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_5396"/>
</filter>
<filter id="filter1_i_0_5396" x="168.084" y="7.65161" width="3.84668" height="4.01465" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_5396"/>
</filter>
<filter id="filter2_i_0_5396" x="0" y="38.5002" width="3.3457" height="3.49976" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_5396"/>
</filter>
<linearGradient id="paint0_linear_0_5396" x1="-86" y1="1.5" x2="-86" y2="41.7612" gradientUnits="userSpaceOnUse">
<stop stop-color="#009EDC" stop-opacity="0.01"/>
<stop offset="1" stop-color="#008EFF"/>
</linearGradient>
<linearGradient id="paint1_linear_0_5396" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_5396" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
<clipPath id="clip0_0_5396">
<rect width="172" height="42" fill="white"/>
</clipPath>
</defs>
</svg>
