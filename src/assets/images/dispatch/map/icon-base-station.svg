<svg width="106" height="132" viewBox="0 0 106 132" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="path-1-inside-1_1772_801" fill="white">
<path d="M53.1743 0C82.349 0 106 24.4681 106 54.6523C105.999 55.6099 105.908 56.5359 105.861 57.4736C105.904 57.9367 106 58.384 106 58.8594C105.999 92.4959 61.3041 127.441 57.2388 130.599C53.1755 133.751 49.1118 130.599 49.1118 130.599C48.9745 130.491 0.35141 92.4494 0.351074 58.8594C0.351074 58.384 0.446382 57.9367 0.489746 57.4736C0.441954 56.5359 0.351103 55.6099 0.351074 54.6523C0.351074 24.4683 24.0009 0.000193638 53.1743 0ZM53.1743 33.6338C41.9525 33.634 32.8589 43.0463 32.8589 54.6523C32.8591 66.266 41.9529 75.6775 53.1743 75.6777C64.3968 75.6777 73.4924 66.2661 73.4927 54.6523C73.4927 43.0462 64.3969 33.6338 53.1743 33.6338Z"/>
</mask>
<g filter="url(#filter0_i_1772_801)">
<path d="M53.1743 0C82.349 0 106 24.4681 106 54.6523C105.999 55.6099 105.908 56.5359 105.861 57.4736C105.904 57.9367 106 58.384 106 58.8594C105.999 92.4959 61.3041 127.441 57.2388 130.599C53.1755 133.751 49.1118 130.599 49.1118 130.599C48.9745 130.491 0.35141 92.4494 0.351074 58.8594C0.351074 58.384 0.446382 57.9367 0.489746 57.4736C0.441954 56.5359 0.351103 55.6099 0.351074 54.6523C0.351074 24.4683 24.0009 0.000193638 53.1743 0ZM53.1743 33.6338C41.9525 33.634 32.8589 43.0463 32.8589 54.6523C32.8591 66.266 41.9529 75.6775 53.1743 75.6777C64.3968 75.6777 73.4924 66.2661 73.4927 54.6523C73.4927 43.0462 64.3969 33.6338 53.1743 33.6338Z" fill="url(#paint0_linear_1772_801)"/>
</g>
<path d="M53.1743 0V-3H53.1743L53.1743 0ZM106 54.6523L109 54.6524V54.6523H106ZM105.861 57.4736L102.865 57.3225L102.854 57.5383L102.874 57.7534L105.861 57.4736ZM106 58.8594L109 58.8594V58.8594H106ZM57.2388 130.599L59.0776 132.969L59.079 132.968L57.2388 130.599ZM49.1118 130.599L47.2639 132.962L47.2731 132.969L49.1118 130.599ZM0.351074 58.8594H-2.64893V58.8594L0.351074 58.8594ZM0.489746 57.4736L3.47668 57.7533L3.49689 57.5375L3.48586 57.3209L0.489746 57.4736ZM0.351074 54.6523H-2.64893V54.6524L0.351074 54.6523ZM53.1743 33.6338V30.6338H53.1743L53.1743 33.6338ZM32.8589 54.6523H29.8589V54.6524L32.8589 54.6523ZM53.1743 75.6777L53.1743 78.6777H53.1743V75.6777ZM73.4927 54.6523L76.4927 54.6524V54.6523H73.4927ZM53.1743 0V3C80.5968 3 103 26.028 103 54.6523H106H109C109 22.9082 84.1012 -3 53.1743 -3V0ZM106 54.6523L103 54.6523C102.999 55.5186 102.922 56.1875 102.865 57.3225L105.861 57.4736L108.857 57.6247C108.894 56.8843 108.999 55.7013 109 54.6524L106 54.6523ZM105.861 57.4736L102.874 57.7534C102.902 58.0502 102.954 58.4095 102.971 58.5417C102.995 58.7326 103 58.8139 103 58.8594H106H109C109 58.4294 108.956 58.0494 108.922 57.782C108.881 57.456 108.863 57.3601 108.848 57.1938L105.861 57.4736ZM106 58.8594L103 58.8593C102.999 74.5031 92.5034 90.921 80.8869 104.182C69.3937 117.302 57.3809 126.69 55.3985 128.229L57.2388 130.599L59.079 132.968C61.1619 131.35 73.5294 121.686 85.4001 108.135C97.1476 94.7251 108.999 76.8522 109 58.8594L106 58.8594ZM57.2388 130.599L55.3999 128.228C54.2676 129.107 53.251 129.096 52.4026 128.876C51.945 128.758 51.5477 128.575 51.2642 128.418C51.126 128.341 51.0248 128.276 50.9681 128.238C50.94 128.219 50.9238 128.208 50.9204 128.205C50.9187 128.204 50.9203 128.205 50.9252 128.209C50.9277 128.211 50.931 128.213 50.9353 128.216C50.9374 128.218 50.9397 128.22 50.9422 128.222C50.9435 128.223 50.9448 128.224 50.9462 128.225C50.9469 128.225 50.948 128.226 50.9483 128.226C50.9494 128.227 50.9505 128.228 49.1118 130.599C47.2731 132.969 47.2742 132.97 47.2754 132.971C47.2758 132.971 47.277 132.972 47.2778 132.973C47.2794 132.974 47.2811 132.975 47.2829 132.977C47.2864 132.979 47.2902 132.982 47.2943 132.985C47.3024 132.992 47.3117 132.999 47.322 133.006C47.3426 133.022 47.3675 133.04 47.3965 133.061C47.4546 133.104 47.5296 133.156 47.6206 133.218C47.8021 133.34 48.0501 133.496 48.3563 133.666C48.9617 134.001 49.8343 134.409 50.9006 134.685C53.0998 135.254 56.1466 135.243 59.0776 132.969L57.2388 130.599ZM49.1118 130.599L50.9597 128.235C50.9656 128.24 47.9548 125.886 43.4383 121.834C38.9191 117.781 32.9148 112.047 26.9243 105.294C20.9256 98.5318 14.9962 90.8088 10.5794 82.7785C6.14974 74.7249 3.35115 66.5601 3.35107 58.8593L0.351074 58.8594L-2.64893 58.8594C-2.64883 67.9536 0.630589 77.1403 5.32215 85.6701C10.0266 94.2232 16.2615 102.315 22.4358 109.276C34.7834 123.195 47.135 132.861 47.2639 132.962L49.1118 130.599ZM0.351074 58.8594H3.35107C3.35107 58.8139 3.35563 58.7327 3.38 58.5417C3.39689 58.4095 3.44888 58.0502 3.47668 57.7533L0.489746 57.4736L-2.49719 57.1939C-2.51275 57.3601 -2.5301 57.4561 -2.5717 57.782C-2.60583 58.0493 -2.64893 58.4294 -2.64893 58.8594H0.351074ZM0.489746 57.4736L3.48586 57.3209C3.42829 56.1914 3.3511 55.5163 3.35107 54.6523L0.351074 54.6523L-2.64893 54.6524C-2.64889 55.7036 -2.54438 56.8804 -2.50637 57.6263L0.489746 57.4736ZM0.351074 54.6523H3.35107C3.35107 26.0281 25.7531 3.00018 53.1743 3L53.1743 0L53.1743 -3C22.2486 -2.99979 -2.64893 22.9085 -2.64893 54.6523H0.351074ZM53.1743 33.6338L53.1743 30.6338C40.1999 30.634 29.8589 41.4868 29.8589 54.6523H32.8589H35.8589C35.8589 44.6058 43.7051 36.634 53.1744 36.6338L53.1743 33.6338ZM32.8589 54.6523L29.8589 54.6524C29.8592 67.8249 40.1998 78.6775 53.1743 78.6777L53.1743 75.6777L53.1744 72.6777C43.7061 72.6776 35.8591 64.7071 35.8589 54.6523L32.8589 54.6523ZM53.1743 75.6777V78.6777C66.1495 78.6777 76.4924 67.8254 76.4927 54.6524L73.4927 54.6523L70.4927 54.6523C70.4925 64.7068 62.644 72.6777 53.1743 72.6777V75.6777ZM73.4927 54.6523H76.4927C76.4927 41.4863 66.1491 30.6338 53.1743 30.6338V33.6338V36.6338C62.6448 36.6338 70.4927 44.6061 70.4927 54.6523H73.4927Z" fill="url(#paint1_linear_1772_801)" mask="url(#path-1-inside-1_1772_801)"/>
<g clip-path="url(#clip0_1772_801)">
<mask id="path-4-inside-2_1772_801" fill="white">
<path d="M52.9995 33.6348C64.2593 33.6349 73.3843 43.0463 73.3843 54.6523C73.3842 66.2661 64.2592 75.6776 52.9995 75.6777C41.7406 75.6777 32.6158 66.2662 32.6157 54.6523C32.6157 43.0462 41.7402 33.6348 52.9995 33.6348Z"/>
</mask>
<g filter="url(#filter1_i_1772_801)">
<path d="M52.9995 33.6348C64.2593 33.6349 73.3843 43.0463 73.3843 54.6523C73.3842 66.2661 64.2592 75.6776 52.9995 75.6777C41.7406 75.6777 32.6158 66.2662 32.6157 54.6523C32.6157 43.0462 41.7402 33.6348 52.9995 33.6348Z" fill="url(#paint2_linear_1772_801)"/>
</g>
<path d="M52.9995 33.6348L52.9996 30.6348H52.9995V33.6348ZM73.3843 54.6523L76.3843 54.6524V54.6523H73.3843ZM52.9995 75.6777V78.6777H52.9996L52.9995 75.6777ZM32.6157 54.6523H29.6157V54.6524L32.6157 54.6523ZM52.9995 33.6348L52.9995 36.6348C62.5164 36.6349 70.3843 44.6158 70.3843 54.6523H73.3843H76.3843C76.3843 41.4768 66.0022 30.635 52.9996 30.6348L52.9995 33.6348ZM73.3843 54.6523L70.3843 54.6523C70.3842 64.6974 62.5156 72.6776 52.9995 72.6777L52.9995 75.6777L52.9996 78.6777C66.0029 78.6775 76.3841 67.8348 76.3843 54.6524L73.3843 54.6523ZM52.9995 75.6777V72.6777C43.4844 72.6777 35.6158 64.6976 35.6157 54.6523L32.6157 54.6523L29.6157 54.6524C29.6159 67.8348 39.9968 78.6777 52.9995 78.6777V75.6777ZM32.6157 54.6523H35.6157C35.6157 44.6155 43.4833 36.6348 52.9995 36.6348V33.6348V30.6348C39.9971 30.6348 29.6157 41.4769 29.6157 54.6523H32.6157Z" fill="url(#paint3_linear_1772_801)" mask="url(#path-4-inside-2_1772_801)"/>
<circle cx="52.6055" cy="54.7646" r="21" fill="#3BE7FF" fill-opacity="0.360784"/>
<path d="M52.9995 33.6348C64.2593 33.6349 73.3843 43.0463 73.3843 54.6523C73.3842 66.2661 64.2592 75.6776 52.9995 75.6777C41.7406 75.6777 32.6158 66.2662 32.6157 54.6523C32.6157 43.0462 41.7402 33.6348 52.9995 33.6348Z" fill="url(#paint4_linear_1772_801)"/>
<g filter="url(#filter2_di_1772_801)">
<path d="M101 54.5C101 80.1812 79.9574 101 54 101C28.0426 101 7 80.1812 7 54.5C7 28.8188 28.0426 8 54 8C79.9574 8 101 28.8188 101 54.5Z" fill="#0BE1FF"/>
<path style="mix-blend-mode:overlay" d="M101 54.5C101 80.1812 79.9574 101 54 101C28.0426 101 7 80.1812 7 54.5C7 28.8188 28.0426 8 54 8C79.9574 8 101 28.8188 101 54.5Z" fill="url(#paint5_linear_1772_801)" fill-opacity="0.58"/>
</g>
<path d="M54 92C74.9868 92 92 75.2107 92 54.5C92 33.7893 74.9868 17 54 17C33.0132 17 16 33.7893 16 54.5C16 75.2107 33.0132 92 54 92Z" fill="#CDFAFF"/>
<g clip-path="url(#clip1_1772_801)">
<path d="M54.3313 84.9116C37.3598 84.6329 23.7775 71.0722 24.0537 54.6788C24.3299 38.1195 38.2708 24.7458 54.9769 25.0915C71.8014 25.4372 84.9927 38.5146 84.9497 55.2256C84.9102 71.7813 70.8366 85.1797 54.3313 84.9116Z" fill="white"/>
<path d="M54.7053 27.6699C69.9946 27.6983 82.5152 40.0669 82.5081 55.1338C82.5009 70.1831 69.9622 82.4671 54.6047 82.4707C39.1325 82.4742 26.5902 70.1728 26.6331 55.0283C26.6761 39.9013 39.2545 27.6417 54.7053 27.6699ZM54.4963 46.3584C53.6274 46.3536 52.7792 46.6215 52.0774 47.123C51.3755 47.6247 50.8559 48.3338 50.5959 49.1455C50.3368 49.9578 50.3516 50.83 50.637 51.6338C50.9225 52.4376 51.4637 53.1315 52.1819 53.6113L45.342 72C45.2053 72.3713 45.2245 72.7808 45.3948 73.1387C45.565 73.4965 45.8729 73.7736 46.2512 73.9102C47.0405 74.1901 47.9126 73.79 48.2024 73.0176L49.6516 69.126H59.2502L60.7317 73.1133C60.9925 73.8113 61.7821 74.1696 62.4954 73.918L62.7698 73.8184C63.483 73.5632 63.849 72.7909 63.592 72.0928L56.7307 53.6357C58.215 52.6647 58.8774 50.861 58.3635 49.1885C58.1129 48.374 57.6016 47.6587 56.9055 47.1494C56.2096 46.6404 55.3651 46.3632 54.4963 46.3584ZM58.2659 66.4785H50.6399L51.6243 63.8281H57.2805L58.2659 66.4785ZM56.2961 61.1768H52.6096L54.4524 56.2266L56.2961 61.1768ZM68.7024 41.0605C68.4436 40.9877 68.1696 40.9837 67.9084 41.0479C67.3762 41.179 66.9593 41.5793 66.8108 42.0967C66.666 42.614 66.8148 43.1667 67.1985 43.5459C70.9063 47.2669 70.8595 53.2135 67.094 56.8779C66.9519 57.0158 66.8383 57.1807 66.761 57.3613C66.6838 57.5418 66.6445 57.736 66.6438 57.9316C66.6432 58.1272 66.6812 58.321 66.7571 58.502C66.8331 58.6831 66.945 58.8485 67.0862 58.9873C67.2271 59.1264 67.3948 59.2369 67.5793 59.3125C67.7639 59.3881 67.9622 59.4271 68.1624 59.4277C68.3623 59.4284 68.5604 59.3907 68.7454 59.3164C68.9304 59.242 69.0987 59.1322 69.2405 58.9941C74.1976 54.1744 74.2596 46.3525 69.3821 41.4619C69.1956 41.272 68.9611 41.1333 68.7024 41.0605ZM41.095 41.0449C40.8339 40.9808 40.5597 40.9849 40.301 41.0576C40.0424 41.1304 39.8078 41.2691 39.6213 41.459C34.7403 46.3495 34.8011 54.175 39.7581 58.9912C39.8999 59.1293 40.069 59.2391 40.2542 59.3135C40.4391 59.3878 40.6372 59.4254 40.8372 59.4248C41.0373 59.4241 41.2356 59.3852 41.4202 59.3096C41.6047 59.2339 41.7724 59.1234 41.9133 58.9844C42.0545 58.8455 42.1664 58.6802 42.2424 58.499C42.3183 58.3181 42.3563 58.1243 42.3557 57.9287C42.355 57.7329 42.3148 57.539 42.2375 57.3584C42.1603 57.1777 42.0476 57.0129 41.9055 56.875C41.0002 55.996 40.2823 54.9495 39.7932 53.7969C39.3041 52.6442 39.0532 51.4082 39.0559 50.1602C39.0495 47.6872 40.0364 45.3101 41.804 43.543C42.1914 43.1638 42.3401 42.6076 42.1917 42.0938C42.1201 41.8395 41.9811 41.6081 41.7883 41.4238C41.5956 41.2396 41.3562 41.109 41.095 41.0449ZM62.8567 44.0996C62.4548 44.0926 62.0643 44.2418 61.7747 44.5146C61.4837 44.7889 61.3151 45.1644 61.3069 45.5596C61.2987 45.9548 61.4513 46.3375 61.7307 46.623C63.7041 48.636 63.6647 51.8257 61.637 53.7891C61.4405 53.9717 61.297 54.2023 61.2219 54.457C61.1469 54.7117 61.1424 54.9814 61.2092 55.2383C61.2764 55.495 61.4126 55.7293 61.6028 55.918C61.793 56.1066 62.0308 56.2424 62.2922 56.3115C62.8244 56.4497 63.3928 56.2979 63.7766 55.9082C66.9848 52.8002 67.05 47.7462 63.925 44.5566C63.787 44.4149 63.6211 44.3011 63.4377 44.2227C63.2544 44.1443 63.0567 44.1025 62.8567 44.0996ZM46.1497 44.0986C45.7459 44.1066 45.3615 44.271 45.0813 44.5557C41.9564 47.7451 42.0217 52.7992 45.2297 55.9072C45.3718 56.0451 45.5406 56.1544 45.7258 56.2285C45.9111 56.3026 46.1097 56.3399 46.3098 56.3389C46.5098 56.3378 46.7075 56.2985 46.8918 56.2227C47.0763 56.1467 47.2443 56.0358 47.385 55.8965C47.6685 55.6152 47.8265 55.235 47.8245 54.8398C47.8223 54.4447 47.6598 54.0665 47.3733 53.7881C45.3457 51.8247 45.3062 48.635 47.2795 46.6221C47.5588 46.3367 47.7114 45.9546 47.7034 45.5596C47.6952 45.1643 47.5265 44.7879 47.2356 44.5137C46.9439 44.2403 46.5534 44.0907 46.1497 44.0986Z" fill="#3BE7FF"/>
</g>
</g>
<rect width="106" height="132" stroke="url(#paint6_linear_1772_801)"/>
<defs>
<filter id="filter0_i_1772_801" x="0.351074" y="0" width="105.648" height="132" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="11"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.82 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1772_801"/>
</filter>
<filter id="filter1_i_1772_801" x="32.6157" y="33.6348" width="40.7686" height="42.043" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="11"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.82 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1772_801"/>
</filter>
<filter id="filter2_di_1772_801" x="-3.5" y="-2.5" width="115" height="114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5.25"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.129412 0 0 0 0 0.517647 0 0 0 0 0.972549 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1772_801"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1772_801" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.75"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1772_801"/>
</filter>
<linearGradient id="paint0_linear_1772_801" x1="0.351074" y1="0" x2="0.351074" y2="132" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BE7FF"/>
<stop offset="1" stop-color="#ACE5FF"/>
</linearGradient>
<linearGradient id="paint1_linear_1772_801" x1="8.79378" y1="0" x2="8.79378" y2="110.903" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BE7FF"/>
<stop offset="0.487497" stop-color="#3BE7FF"/>
<stop offset="1" stop-color="#3BE7FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_1772_801" x1="32.6157" y1="33.6348" x2="32.6157" y2="75.6777" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BE7FF"/>
<stop offset="1" stop-color="#13B8FF"/>
</linearGradient>
<linearGradient id="paint3_linear_1772_801" x1="35.8737" y1="33.6348" x2="35.8737" y2="68.9581" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BE7FF"/>
<stop offset="0.487497" stop-color="#3BE7FF"/>
<stop offset="1" stop-color="#3BE7FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint4_linear_1772_801" x1="32.6157" y1="33.6348" x2="32.6157" y2="75.6777" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.596155" stop-color="white" stop-opacity="0.01"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_1772_801" x1="54" y1="8" x2="54" y2="101" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_1772_801" x1="8.47081" y1="0" x2="8.47081" y2="110.903" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BE7FF"/>
<stop offset="0.487497" stop-color="#3BE7FF"/>
<stop offset="1" stop-color="#3BE7FF" stop-opacity="0.01"/>
</linearGradient>
<clipPath id="clip0_1772_801">
<rect width="106" height="132" fill="white"/>
</clipPath>
<clipPath id="clip1_1772_801">
<rect width="61" height="60" fill="white" transform="translate(24 25)"/>
</clipPath>
</defs>
</svg>
