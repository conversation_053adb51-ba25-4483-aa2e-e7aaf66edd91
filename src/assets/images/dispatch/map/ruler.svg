<svg width="86" height="85" viewBox="0 0 86 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_1923)">
<g clip-path="url(#clip1_0_1923)">
<path d="M42.6514 56.1372C42.8775 56.0602 43.1225 56.0602 43.3486 56.1372L82.0039 69.3159C82.9803 69.6492 82.9804 71.0315 82.0039 71.3647L43.3486 84.5435C43.1226 84.6205 42.8774 84.6205 42.6514 84.5435L3.99609 71.3647C3.0196 71.0315 3.01966 69.6492 3.99609 69.3159L42.6514 56.1372Z" fill="url(#paint0_linear_0_1923)" stroke="url(#paint1_linear_0_1923)" stroke-width="0.64"/>
<path d="M42.7549 48.1196C42.9138 48.0655 43.0862 48.0655 43.2451 48.1196L81.9004 61.3101C82.5883 61.545 82.5884 62.5215 81.9004 62.7563L43.2451 75.9468C43.0862 76.0009 42.9138 76.0009 42.7549 75.9468L4.09961 62.7563C3.41163 62.5215 3.41168 61.545 4.09961 61.3101L42.7549 48.1196Z" fill="url(#paint2_linear_0_1923)" stroke="url(#paint3_linear_0_1923)" stroke-width="1.28"/>
<path d="M42.7549 40.4399C42.9138 40.3857 43.0862 40.3857 43.2451 40.4399L81.9004 53.6382C82.588 53.8734 82.5881 54.8492 81.9004 55.0845L43.2451 68.2827C43.0862 68.3369 42.9138 68.3369 42.7549 68.2827L4.09961 55.0845C3.41195 54.8492 3.41196 53.8734 4.09961 53.6382L42.7549 40.4399Z" fill="url(#paint4_linear_0_1923)" stroke="url(#paint5_linear_0_1923)" stroke-width="1.28"/>
<path d="M73.9022 12.9629V45.1182L43.3504 61.2676L12.7996 45.1182V12.957L42.907 0.689453L73.9022 12.9629Z" fill="url(#paint6_linear_0_1923)" stroke="url(#paint7_linear_0_1923)" stroke-width="1.28"/>
<g filter="url(#filter0_f_0_1923)">
<path d="M43.0903 46.0801C52.043 46.0801 59.3007 47.0799 59.3007 48.3132C59.3007 49.5465 52.043 50.5463 43.0903 50.5463C34.1376 50.5463 26.88 49.5465 26.88 48.3132C26.88 47.0799 34.1376 46.0801 43.0903 46.0801V46.0801Z" fill="black" fill-opacity="0.8"/>
</g>
</g>
<rect opacity="0.01" x="25" y="14" width="36" height="36" fill="black"/>
<path d="M48.3686 46.7785C48.3686 47.3308 47.9208 47.7785 47.3686 47.7785H45.8985C45.3462 47.7785 44.8985 47.3308 44.8985 46.7785V43.5734C44.8985 43.0211 44.4508 42.5734 43.8985 42.5734H42.4284C41.8761 42.5734 41.4284 43.0211 41.4284 43.5734V46.7785C41.4284 47.3308 40.9807 47.7785 40.4284 47.7785H38.9583C38.406 47.7785 37.9583 47.3308 37.9583 46.7785V45.3085C37.9583 44.7562 37.5106 44.3085 36.9583 44.3085H35.4883C34.936 44.3085 34.4883 44.7562 34.4883 45.3085V46.7785C34.4883 47.3308 34.0405 47.7785 33.4883 47.7785H29.2831C28.823 47.7785 28.3817 47.5957 28.0563 47.2704C27.7309 46.945 27.5481 46.5037 27.5481 46.0435V41.8384C27.5481 41.2861 27.9958 40.8384 28.5481 40.8384H30.0182C30.5704 40.8384 31.0182 40.3907 31.0182 39.8384V38.3683C31.0182 37.816 30.5704 37.3683 30.0182 37.3683H28.5481C27.9958 37.3683 27.5481 36.9206 27.5481 36.3683V34.8982C27.5481 34.346 27.9958 33.8982 28.5481 33.8982H31.7532C32.3055 33.8982 32.7532 33.4505 32.7532 32.8982V31.4281C32.7532 30.8759 32.3055 30.4281 31.7532 30.4281H28.5481C27.9958 30.4281 27.5481 29.9804 27.5481 29.4281V27.9581C27.5481 27.4058 27.9958 26.9581 28.5481 26.9581H30.0182C30.5704 26.9581 31.0182 26.5104 31.0182 25.9581V24.488C31.0182 23.9357 30.5704 23.488 30.0182 23.488H28.5481C27.9958 23.488 27.5481 23.0403 27.5481 22.488V18.2829C27.5481 17.3247 28.3249 16.5479 29.2831 16.5479H39.6934C40.6516 16.5479 41.4284 17.3247 41.4284 18.2829V32.8982C41.4284 33.4505 41.8761 33.8982 42.4284 33.8982H57.0437C58.002 33.8982 58.7788 34.675 58.7788 35.6333V46.0435C58.7788 46.5037 58.596 46.945 58.2706 47.2704C57.9452 47.5957 57.5039 47.7785 57.0437 47.7785H52.8386C52.2863 47.7785 51.8386 47.3308 51.8386 46.7785V45.3085C51.8386 44.7562 51.3909 44.3085 50.8386 44.3085H49.3686C48.8163 44.3085 48.3686 44.7562 48.3686 45.3085V46.7785Z" fill="url(#paint8_linear_0_1923)"/>
</g>
<defs>
<filter id="filter0_f_0_1923" x="19.2302" y="38.4302" width="47.7203" height="19.766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.82492" result="effect1_foregroundBlur_0_1923"/>
</filter>
<linearGradient id="paint0_linear_0_1923" x1="-4.56772" y1="68.7829" x2="-4.56772" y2="85.0002" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1923" x1="-43" y1="55.6802" x2="-43" y2="85.0002" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1923" x1="-4.56772" y1="60.4744" x2="-4.56772" y2="76.7063" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint3_linear_0_1923" x1="-43" y1="47.3599" x2="-43" y2="76.7063" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint4_linear_0_1923" x1="-4.56772" y1="52.8018" x2="-4.56772" y2="69.0424" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8"/>
<stop offset="1" stop-color="#62B7F2"/>
</linearGradient>
<linearGradient id="paint5_linear_0_1923" x1="-43" y1="39.6802" x2="-43" y2="69.0424" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.247059"/>
<stop offset="1" stop-color="#8CF1FF"/>
</linearGradient>
<linearGradient id="paint6_linear_0_1923" x1="5.2569" y1="0" x2="5.2569" y2="37.8562" gradientUnits="userSpaceOnUse">
<stop stop-color="#47B7FF"/>
<stop offset="0.999648" stop-color="#003EFF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#003EFF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint7_linear_0_1923" x1="-1.42616" y1="0" x2="-1.42616" y2="44.4975" gradientUnits="userSpaceOnUse">
<stop stop-color="#EEF9FF"/>
<stop offset="1" stop-color="#D9F0FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint8_linear_0_1923" x1="8.06064" y1="12.6757" x2="8.06064" y2="47.7785" gradientUnits="userSpaceOnUse">
<stop stop-color="#D6F4FF"/>
<stop offset="1" stop-color="#2085FF"/>
</linearGradient>
<clipPath id="clip0_0_1923">
<rect width="86" height="85" fill="white"/>
</clipPath>
<clipPath id="clip1_0_1923">
<rect width="86" height="85" fill="white"/>
</clipPath>
</defs>
</svg>
