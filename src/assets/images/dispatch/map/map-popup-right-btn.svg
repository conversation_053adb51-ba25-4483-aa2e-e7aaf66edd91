<svg width="172" height="42" viewBox="0 0 172 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_5403)">
<g filter="url(#filter0_i_0_5403)">
<path d="M35 7.42683L5.95003 7.58476L0.120667 13.2029C0.0427941 13.2936 0 13.4083 0 13.5268V41.258C0 41.536 0.230404 41.7612 0.514664 41.7612H165.885L171.88 34.7653C171.957 34.6747 172 34.5603 172 34.4421V2.00327C172 1.7253 171.77 1.5 171.485 1.5H39L35 7.42683Z" fill="#0065B6" fill-opacity="0.176471"/>
</g>
<path d="M171.485 1.75586H39.1357L35.2119 7.57031L35.1367 7.68164L35.001 7.68262L6.05469 7.83984L0.311523 13.375C0.275682 13.4189 0.25595 13.472 0.255859 13.5264V41.2578C0.255859 41.389 0.366344 41.5049 0.514648 41.5049H165.768L171.686 34.5986C171.724 34.554 171.744 34.4987 171.744 34.4424V2.00293C171.744 1.87186 171.634 1.75586 171.485 1.75586Z" stroke="url(#paint0_linear_0_5403)" stroke-width="0.512"/>
<g filter="url(#filter1_i_0_5403)">
<path d="M0.0694313 11.6662V7.65161H3.91602L0.0694313 11.6662V11.6662Z" fill="#008EFF"/>
</g>
<g filter="url(#filter2_i_0_5403)">
<path d="M172 38.5003V42H168.654L172 38.5003V38.5003Z" fill="#008EFF"/>
</g>
<path d="M172 0.494118H39" stroke="url(#paint1_linear_0_5403)" stroke-width="0.512"/>
<path d="M35.2559 7.76871L4.99988 7.49498" stroke="url(#paint2_linear_0_5403)" stroke-width="0.512"/>
</g>
<defs>
<filter id="filter0_i_0_5403" x="0" y="-1.5" width="172" height="43.2612" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_5403"/>
</filter>
<filter id="filter1_i_0_5403" x="0.0693359" y="7.65161" width="3.84668" height="4.01465" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_5403"/>
</filter>
<filter id="filter2_i_0_5403" x="168.654" y="38.5002" width="3.3457" height="3.49976" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_5403"/>
</filter>
<linearGradient id="paint0_linear_0_5403" x1="258" y1="1.5" x2="258" y2="41.7612" gradientUnits="userSpaceOnUse">
<stop stop-color="#009EDC" stop-opacity="0.01"/>
<stop offset="1" stop-color="#008EFF"/>
</linearGradient>
<linearGradient id="paint1_linear_0_5403" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_5403" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
<clipPath id="clip0_0_5403">
<rect width="172" height="42" fill="white" transform="matrix(-1 0 0 1 172 0)"/>
</clipPath>
</defs>
</svg>
