<svg width="86" height="85" viewBox="0 0 86 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_1854)">
<g clip-path="url(#clip1_0_1854)">
<path d="M42.6514 56.1372C42.8775 56.0602 43.1225 56.0602 43.3486 56.1372L82.0039 69.3159C82.9803 69.6492 82.9804 71.0315 82.0039 71.3647L43.3486 84.5435C43.1226 84.6205 42.8774 84.6205 42.6514 84.5435L3.99609 71.3647C3.0196 71.0315 3.01966 69.6492 3.99609 69.3159L42.6514 56.1372Z" fill="url(#paint0_linear_0_1854)" stroke="url(#paint1_linear_0_1854)" stroke-width="0.64"/>
<path d="M42.7549 48.1196C42.9138 48.0655 43.0862 48.0655 43.2451 48.1196L81.9004 61.3101C82.5883 61.545 82.5884 62.5215 81.9004 62.7563L43.2451 75.9468C43.0862 76.0009 42.9138 76.0009 42.7549 75.9468L4.09961 62.7563C3.41163 62.5215 3.41168 61.545 4.09961 61.3101L42.7549 48.1196Z" fill="url(#paint2_linear_0_1854)" stroke="url(#paint3_linear_0_1854)" stroke-width="1.28"/>
<path d="M42.7549 40.4399C42.9138 40.3857 43.0862 40.3857 43.2451 40.4399L81.9004 53.6382C82.588 53.8734 82.5881 54.8492 81.9004 55.0845L43.2451 68.2827C43.0862 68.3369 42.9138 68.3369 42.7549 68.2827L4.09961 55.0845C3.41195 54.8492 3.41196 53.8734 4.09961 53.6382L42.7549 40.4399Z" fill="url(#paint4_linear_0_1854)" stroke="url(#paint5_linear_0_1854)" stroke-width="1.28"/>
<path d="M73.9022 12.9629V45.1182L43.3504 61.2676L12.7996 45.1182V12.957L42.907 0.689453L73.9022 12.9629Z" fill="url(#paint6_linear_0_1854)" stroke="url(#paint7_linear_0_1854)" stroke-width="1.28"/>
<g filter="url(#filter0_f_0_1854)">
<path d="M43.0903 46.0801C52.043 46.0801 59.3007 47.0799 59.3007 48.3132C59.3007 49.5465 52.043 50.5463 43.0903 50.5463C34.1376 50.5463 26.88 49.5465 26.88 48.3132C26.88 47.0799 34.1376 46.0801 43.0903 46.0801V46.0801Z" fill="black" fill-opacity="0.8"/>
</g>
</g>
<rect opacity="0.01" x="25" y="14" width="36" height="36" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M28 32.5844C28 23.9773 34.9778 17 43.5844 17C52.1915 17 59.1688 23.9773 59.1688 32.5844C59.1688 41.1915 52.1915 48.1688 43.5844 48.1688C34.9778 48.1688 28 41.1915 28 32.5844ZM32 33C32 39.6167 37.3833 45 44 45C50.6167 45 56 39.6167 56 33C56 26.3833 50.6167 21 44 21C37.3833 21 32 26.3833 32 33ZM46.0103 33.3578L46.8198 26.1396L42.3196 31.8397L46.0103 33.3578ZM41.1801 39.8604L45.6804 34.1603L41.9897 32.6422L41.1801 39.8604ZM44 21.8675C37.8617 21.8675 32.8675 26.8617 32.8675 33C32.8675 39.1383 37.8617 44.1325 44 44.1325C50.1383 44.1325 55.1325 39.1383 55.1325 33C55.1325 26.8617 50.1383 21.8675 44 21.8675ZM33.7191 33.4225H35.004C35.2434 33.4225 35.4377 33.2281 35.4377 32.9887C35.4377 32.7492 35.2434 32.555 35.004 32.555H33.7191C33.4797 32.555 33.2854 32.7492 33.2854 32.9887C33.2854 33.2281 33.4797 33.4225 33.7191 33.4225ZM43.5663 22.7078C43.5663 22.4684 43.7605 22.2741 44 22.2741C44.2395 22.2741 44.4337 22.4684 44.4337 22.7078V23.9927C44.4337 24.2321 44.2395 24.4264 44 24.4264C43.7605 24.4264 43.5663 24.2321 43.5663 23.9927V22.7078ZM44 43.7033C44.2395 43.7033 44.4337 43.509 44.4337 43.2696V41.9847C44.4337 41.7453 44.2395 41.551 44 41.551C43.7605 41.551 43.5663 41.7453 43.5663 41.9847V43.2696C43.5663 43.509 43.7605 43.7033 44 43.7033ZM46.808 34.0259C46.8035 34.065 46.7937 34.1033 46.7789 34.1397L46.7786 34.1411L46.7783 34.1425C46.7776 34.1444 46.7765 34.146 46.7754 34.1476C46.7744 34.1491 46.7734 34.1505 46.7727 34.1521C46.758 34.1854 46.7402 34.2176 46.7176 34.2464L40.9204 41.5894C40.8002 41.7412 40.5941 41.7953 40.4149 41.7218C40.2359 41.6481 40.1273 41.4647 40.1489 41.2723L41.192 31.9741C41.1965 31.9348 41.2064 31.8967 41.2211 31.8603C41.2214 31.8595 41.2214 31.8583 41.2217 31.8575C41.2219 31.8567 41.2228 31.8563 41.2231 31.8555C41.2381 31.8191 41.2578 31.7846 41.2824 31.7535L47.0796 24.4106C47.1997 24.2587 47.4061 24.2051 47.5851 24.2782C47.7642 24.3519 47.8727 24.5353 47.8511 24.7277L46.808 34.0259Z" fill="url(#paint8_linear_0_1854)"/>
</g>
<defs>
<filter id="filter0_f_0_1854" x="19.2302" y="38.4302" width="47.7203" height="19.766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.82492" result="effect1_foregroundBlur_0_1854"/>
</filter>
<linearGradient id="paint0_linear_0_1854" x1="-4.56772" y1="68.7829" x2="-4.56772" y2="85.0002" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1854" x1="-43" y1="55.6802" x2="-43" y2="85.0002" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1854" x1="-4.56772" y1="60.4744" x2="-4.56772" y2="76.7063" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint3_linear_0_1854" x1="-43" y1="47.3599" x2="-43" y2="76.7063" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint4_linear_0_1854" x1="-4.56772" y1="52.8018" x2="-4.56772" y2="69.0424" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8"/>
<stop offset="1" stop-color="#62B7F2"/>
</linearGradient>
<linearGradient id="paint5_linear_0_1854" x1="-43" y1="39.6802" x2="-43" y2="69.0424" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.247059"/>
<stop offset="1" stop-color="#8CF1FF"/>
</linearGradient>
<linearGradient id="paint6_linear_0_1854" x1="5.2569" y1="0" x2="5.2569" y2="37.8562" gradientUnits="userSpaceOnUse">
<stop stop-color="#47B7FF"/>
<stop offset="0.999648" stop-color="#003EFF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#003EFF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint7_linear_0_1854" x1="-1.42616" y1="0" x2="-1.42616" y2="44.4975" gradientUnits="userSpaceOnUse">
<stop stop-color="#EEF9FF"/>
<stop offset="1" stop-color="#D9F0FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint8_linear_0_1854" x1="8.55115" y1="13.1356" x2="8.55115" y2="48.1688" gradientUnits="userSpaceOnUse">
<stop stop-color="#D6F4FF"/>
<stop offset="1" stop-color="#2085FF"/>
</linearGradient>
<clipPath id="clip0_0_1854">
<rect width="86" height="85" fill="white"/>
</clipPath>
<clipPath id="clip1_0_1854">
<rect width="86" height="85" fill="white"/>
</clipPath>
</defs>
</svg>
