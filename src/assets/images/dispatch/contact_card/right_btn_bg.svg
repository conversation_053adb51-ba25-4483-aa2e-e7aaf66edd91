<svg width="112" height="48" viewBox="0 0 112 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_9671)">
<g filter="url(#filter0_i_0_9671)">
<path d="M39.9558 8.1044L5.65717 7.959L0.137909 14.4582C0.0489119 14.5632 0 14.6958 0 14.8329V46.9131C0 47.2346 0.263313 47.4952 0.588181 47.4952H105.011L111.863 39.4022C111.951 39.2974 112 39.165 112 39.0283V1.50217C112 1.18062 111.737 0.919983 111.412 0.919983H44.073L39.9558 8.1044Z" fill="#0065B6" fill-opacity="0.176471"/>
</g>
<path d="M111.412 1.21393H44.2432L40.2109 8.25104L40.126 8.39948L39.9541 8.3985L5.79395 8.25299L0.362305 14.6485C0.318101 14.7006 0.293945 14.7663 0.293945 14.8331V46.9131C0.293993 47.0691 0.422936 47.2011 0.587891 47.2012H104.874L111.638 39.212C111.682 39.1601 111.706 39.0951 111.706 39.0284V1.50201C111.706 1.34604 111.577 1.21408 111.412 1.21393Z" stroke="url(#paint0_linear_0_9671)" stroke-width="0.5888"/>
<g filter="url(#filter1_i_0_9671)">
<path d="M0.0676594 12.8733V8.22437H3.80371L0.0676594 12.8733V12.8733Z" fill="#008EFF"/>
</g>
<g filter="url(#filter2_i_0_9671)">
<path d="M112 43.7003V48H108.177L112 43.7003V43.7003Z" fill="#008EFF"/>
</g>
<path d="M112 0L44.0656 0.502609" stroke="url(#paint1_linear_0_9671)" stroke-width="0.5888"/>
<path d="M38.6943 7.2193L0.457111 8.74598" stroke="url(#paint2_linear_0_9671)" stroke-width="0.5888"/>
</g>
<defs>
<filter id="filter0_i_0_9671" x="0" y="-3.08002" width="112" height="50.5753" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_9671"/>
</filter>
<filter id="filter1_i_0_9671" x="0.0678711" y="8.22437" width="3.73584" height="4.64886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_9671"/>
</filter>
<filter id="filter2_i_0_9671" x="108.177" y="43.7003" width="3.82324" height="4.29968" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_9671"/>
</filter>
<linearGradient id="paint0_linear_0_9671" x1="168" y1="0.919983" x2="168" y2="47.4952" gradientUnits="userSpaceOnUse">
<stop stop-color="#009EDC" stop-opacity="0.01"/>
<stop offset="1" stop-color="#008EFF"/>
</linearGradient>
<linearGradient id="paint1_linear_0_9671" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_9671" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
<clipPath id="clip0_0_9671">
<rect width="112" height="48" fill="white" transform="matrix(-1 0 0 1 112 0)"/>
</clipPath>
</defs>
</svg>
