<svg width="112" height="40" viewBox="0 0 112 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_9915)">
<g filter="url(#filter0_i_0_9915)">
<path d="M39.9558 0.145259L5.65717 0L0.137909 6.4926C0.0489119 6.59743 0 6.72994 0 6.86682V38.9142C0 39.2354 0.263313 39.4958 0.588181 39.4958H105.011L111.863 31.411C111.951 31.3063 112 31.1741 112 31.0375V0.927325C112 0.606099 111.737 0.345731 111.412 0.345731H44.073L39.9558 0.145259Z" fill="#0065B6" fill-opacity="0.176471"/>
</g>
<path d="M0.362305 6.68359C0.318293 6.73557 0.293945 6.80075 0.293945 6.86719V38.9141C0.293945 39.0695 0.422621 39.201 0.587891 39.2012H104.876L111.638 31.2207C111.682 31.1689 111.706 31.1036 111.706 31.0371V0.927734C111.706 0.772304 111.577 0.6398 111.412 0.639648H44.0586L39.9424 0.438477L5.79395 0.293945L0.362305 6.68359Z" stroke="url(#paint0_linear_0_9915)" stroke-width="0.5888"/>
<g filter="url(#filter1_i_0_9915)">
<path d="M0.0676594 4.93061V0.289307H3.80371L0.0676594 4.93061V4.93061Z" fill="#008EFF"/>
</g>
<g filter="url(#filter2_i_0_9915)">
<path d="M112 35.7652V40H108.177L112 35.7652V35.7652Z" fill="#008EFF"/>
</g>
</g>
<defs>
<filter id="filter0_i_0_9915" x="0" y="-4" width="112" height="43.4958" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_9915"/>
</filter>
<filter id="filter1_i_0_9915" x="0.0678711" y="0.289307" width="3.73584" height="4.6413" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_9915"/>
</filter>
<filter id="filter2_i_0_9915" x="108.177" y="35.7652" width="3.82324" height="4.23477" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_9915"/>
</filter>
<linearGradient id="paint0_linear_0_9915" x1="168" y1="0" x2="168" y2="39.4958" gradientUnits="userSpaceOnUse">
<stop stop-color="#009EDC" stop-opacity="0.01"/>
<stop offset="1" stop-color="#008EFF"/>
</linearGradient>
<clipPath id="clip0_0_9915">
<rect width="112" height="40" fill="white" transform="matrix(-1 0 0 1 112 0)"/>
</clipPath>
</defs>
</svg>
