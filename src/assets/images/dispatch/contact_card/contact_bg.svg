<svg width="410" height="217" viewBox="0 0 410 217" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_9551)">
<g filter="url(#filter0_i_0_9551)">
<path d="M14.1277 0H395.702L410 15.1228V54.25V108.5V162.75V189.875V202.438L395.872 217H14.1277L0 202.438V189.875V162.75V108.5V54.25V27.125V14.5625L14.1277 0Z" fill="#062645"/>
</g>
<path d="M395.452 0.580078L409.42 15.3535V202.201L395.627 216.42H14.373L0.580078 202.201V14.7979L14.373 0.580078H395.452Z" stroke="#035C9E" stroke-width="1.16104"/>
<mask id="mask0_0_9551" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="410" height="217">
<path d="M395.452 0.580078L409.42 15.3535V202.201L395.627 216.42H14.373L0.580078 202.201V14.7979L14.373 0.580078H395.452Z" fill="white" stroke="white" stroke-width="1.16104"/>
</mask>
<g mask="url(#mask0_0_9551)">
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M390.533 4.57143H343.806L338 0H396.106L410 13.6737V40L405.355 34.2857V19.1568L390.533 4.57143Z" fill="#0096FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3991 212.429H66.1935L72 217H13.8942L0 203.326V177L4.64516 182.714V198.15L19.3991 212.429Z" fill="#0096FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.0576 4.57143L66.1935 4.57143L72 0L13.8942 0L0 13.6737V40L4.64516 34.2857V19.7308L20.0576 4.57143Z" fill="#0096FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M390.031 212.429H343.806L338 217H396.106L410 203.326V177L405.355 182.714V197.591L390.031 212.429Z" fill="#0096FF"/>
</g>
<defs>
<filter id="filter0_i_0_9551" x="0" y="0" width="410" height="217" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="15.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.584314 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_9551"/>
</filter>
<clipPath id="clip0_0_9551">
<rect width="410" height="217" fill="white"/>
</clipPath>
</defs>
</svg>
