/**
 * DataManager 数据管理器类型定义
 * 该文件包含所有数据管理器相关的 TypeScript 类型定义
 */
/* eslint-disable @typescript-eslint/no-explicit-any */

// 通用数据类型
type DataRecord = Record<string, any>
type IndexType = string | number | any[]

// DataManage 基类类型定义
export interface DataManageBase {
  name: string
  get(key?: string): any
  set(key?: string, val?: any, index?: any): this
  delete(key?: string): this
  getAll(): DataRecord
  getParent(key?: string): any
  getOrgNameByKey(key?: string, bool?: boolean): string
  getList(): DataRecord
  setList(key?: string, val?: any): this
  deleteList(key?: string): this
  deleteIndexByVal(val?: string): void
  deleteIndexByKey(key?: string): void
  getDataByIndex(index: any): any
  getIndex(key?: any): any
  setIndex(key?: IndexType, val?: string): this
  getMarkerAll(): DataRecord
  getMarker(key?: string): any
  setMarker(key?: string, marker: any): this
  deleteMarker(key?: string): this
  clear(): void
}

// ImagesData 图片数据管理器类型定义
export interface ImagesDataType extends DataManageBase {
  getFileContentBykey(key?: string): string
  getFileNameBykey(key?: string): string
  getHashBykey(key?: string): string
}

// OrgsData 单位数据管理器类型定义
export interface OrgsDataType extends DataManageBase {
  getOrgMapMakerPointLonlatInfo(orgRid: string): { lonlat?: [number, number], showLevel?: number }
  getOrgMapMakerPoint(orgRid: string): any
  getList(virtual?: number): DataRecord
  setList(key?: string, val?: any, virtual?: number): this
  deleteList(key?: string, virtual?: number): this
  deleteAllData(key?: string): void
  delete(key?: string): this
  getShortName(key?: string): string
  getFullName(key?: string): string
  getDynamicGroup(): any[]
  getDataMaybeNoPerm(rid?: string): any
}

// DynamicGroupDetail 动态组详情管理器类型定义
export interface DynamicGroupDetailType extends DataManageBase {
  getDataByGroupRid(groupRid: string): any[]
  deleteDataByGroupRid(groupRid: string): this
  getDetailSource(rid: string): any
  getDetailParentSource(memberOrgId: string): any
  getDynamicGroup(orgId: string): any
  sortDetails(dataList: any[]): any[]
  getSortDataByGroupRid(groupRid: string): any[]
}

// JobsData 职位数据管理器类型定义
export interface JobsDataType extends DataManageBase {
  getJobNameByKey(key?: string): string
}

// UsersData 用户数据管理器类型定义
export interface UsersDataType extends DataManageBase {
  getJobNameByKey(key?: string): string
  getUserNameByKey(key?: string): string
  getUserImageByKey(key?: string): string
  setPrivelege(key?: string, val?: any[]): this
  getPrivelege(key?: string): any
  deletePrivelege(key?: string): void
  delete(key?: string): this
  clear(): void
}

// DevicesData 设备数据管理器类型定义
export interface DevicesDataType extends DataManageBase {
  getUserNameByKey(key?: string): string
  getUserImageFileByKey(key?: string): string
  getSelfIdByKey(key?: string): string
  getDataMaybeNoPerm(rid?: string): any
}

// 对于暂时没有特殊方法的数据管理器，使用类型别名而不是空接口
export type MapPointsDataType = DataManageBase
export type LinePointsDataType = DataManageBase
export type LineMastersDataType = DataManageBase
export type AppMapPrivilegeDeviceDataType = DataManageBase

export interface BfglobDataManager {
  // 各种数据管理器实例
  gimages: ImagesDataType
  gorgData: OrgsDataType
  gdynamicGroupDetail: DynamicGroupDetailType
  gjobData: JobsDataType
  guserData: UsersDataType
  gdevices: DevicesDataType
  gmapPoints: MapPointsDataType
  glinePoints: LinePointsDataType
  glineMaster: LineMastersDataType
  gruleMaster: DataManageBase
  gcontrollers: DataManageBase
  gshortNo: DataManageBase
  gatewayFilter: DataManageBase
  gatewayPermission: DataManageBase
  gcontrollerGateway: DataManageBase
  gphoneBook: DataManageBase
  gGroupCallContacts: DataManageBase
  gSingleCallContacts: DataManageBase
  gappMapPrivilegeDevice: AppMapPrivilegeDeviceDataType
  noPermOrgData: DataManageBase
}