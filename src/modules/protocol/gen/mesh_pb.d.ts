// @generated by protoc-gen-es v1.10.1 with parameter "target=js+dts,import_extension=none"
// @generated from file mesh.proto (package bfdx_proto, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from '@bufbuild/protobuf'
import { Message, proto3 } from '@bufbuild/protobuf'

/**
 * @generated from enum bfdx_proto.RespCode
 */
export declare enum RespCode {
  /**
   * unused
   *
   * @generated from enum value: RespCode_Unused = 0;
   */
  RespCode_Unused = 0,

  /**
   * ok
   *
   * @generated from enum value: RespCode_OK = 1;
   */
  RespCode_OK = 1,

  /**
   * common err code
   *
   * @generated from enum value: RespCode_Err = 2;
   */
  RespCode_Err = 2,

  /**
   * bad request
   *
   * @generated from enum value: RespCode_Bad_Request = 3;
   */
  RespCode_Bad_Request = 3,

  /**
   * busy
   *
   * @generated from enum value: RespCode_Busy = 4;
   */
  RespCode_Busy = 4,

  /**
   * can't match board for target
   *
   * @generated from enum value: RespCode_Cant_Match_Board = 5;
   */
  RespCode_Cant_Match_Board = 5,

  /**
   * not found
   *
   * @generated from enum value: RespCode_Not_Found = 6;
   */
  RespCode_Not_Found = 6,
}

/**
 * @generated from message bfdx_proto.CommonReq
 */
export declare class CommonReq extends Message<CommonReq> {
  /**
   * sid
   *
   * @generated from field: string Sid = 1;
   */
  Sid: string

  /**
   * str data
   *
   * @generated from field: string StrData = 2;
   */
  StrData: string

  /**
   * int data
   *
   * @generated from field: fixed32 IntData = 3;
   */
  IntData: number

  /**
   * binary data
   *
   * @generated from field: bytes BinData = 4;
   */
  BinData: Uint8Array

  constructor(data?: PartialMessage<CommonReq>)

  static readonly runtime: typeof proto3
  static readonly typeName = 'bfdx_proto.CommonReq'
  static readonly fields: FieldList

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CommonReq

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CommonReq

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CommonReq

  static equals(a: CommonReq | PlainMessage<CommonReq> | undefined, b: CommonReq | PlainMessage<CommonReq> | undefined): boolean
}

/**
 * @generated from message bfdx_proto.CommonResp
 */
export declare class CommonResp extends Message<CommonResp> {
  /**
   * @generated from field: bfdx_proto.RespCode Code = 1;
   */
  Code: RespCode

  /**
   * resp info
   *
   * @generated from field: string RespInfo = 2;
   */
  RespInfo: string

  /**
   * str data
   *
   * @generated from field: string StrData = 3;
   */
  StrData: string

  /**
   * int data
   *
   * @generated from field: fixed32 IntData = 4;
   */
  IntData: number

  /**
   * binary data
   *
   * @generated from field: bytes BinData = 5;
   */
  BinData: Uint8Array

  constructor(data?: PartialMessage<CommonResp>)

  static readonly runtime: typeof proto3
  static readonly typeName = 'bfdx_proto.CommonResp'
  static readonly fields: FieldList

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CommonResp

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CommonResp

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CommonResp

  static equals(a: CommonResp | PlainMessage<CommonResp> | undefined, b: CommonResp | PlainMessage<CommonResp> | undefined): boolean
}

/**
 * @generated from message bfdx_proto.Gateway8100SubsInfo
 */
export declare class Gateway8100SubsInfo extends Message<Gateway8100SubsInfo> {
  /**
   * all subscribe group dmrid
   *
   * @generated from field: repeated fixed32 DMRIDs = 1;
   */
  DMRIDs: number[]

  constructor(data?: PartialMessage<Gateway8100SubsInfo>)

  static readonly runtime: typeof proto3
  static readonly typeName = 'bfdx_proto.Gateway8100SubsInfo'
  static readonly fields: FieldList

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Gateway8100SubsInfo

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Gateway8100SubsInfo

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Gateway8100SubsInfo

  static equals(
    a: Gateway8100SubsInfo | PlainMessage<Gateway8100SubsInfo> | undefined,
    b: Gateway8100SubsInfo | PlainMessage<Gateway8100SubsInfo> | undefined
  ): boolean
}
