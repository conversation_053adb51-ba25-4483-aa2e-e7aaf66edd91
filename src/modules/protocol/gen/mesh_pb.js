// @generated by protoc-gen-es v1.10.1 with parameter "target=js+dts,import_extension=none"
// @generated from file mesh.proto (package bfdx_proto, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from '@bufbuild/protobuf'

/**
 * @generated from enum bfdx_proto.RespCode
 */
export const RespCode = /*@__PURE__*/ proto3.makeEnum('bfdx_proto.RespCode', [
  { no: 0, name: 'RespCode_Unused' },
  { no: 1, name: 'RespCode_OK' },
  { no: 2, name: 'RespCode_Err' },
  { no: 3, name: 'RespCode_Bad_Request' },
  { no: 4, name: 'RespCode_Busy' },
  { no: 5, name: 'RespCode_Cant_Match_Board' },
  { no: 6, name: 'RespCode_Not_Found' },
])

/**
 * @generated from message bfdx_proto.CommonReq
 */
export const CommonReq = /*@__PURE__*/ proto3.makeMessageType('bfdx_proto.CommonReq', () => [
  { no: 1, name: 'Sid', kind: 'scalar', T: 9 /* ScalarType.STRING */ },
  { no: 2, name: 'StrData', kind: 'scalar', T: 9 /* ScalarType.STRING */ },
  { no: 3, name: 'IntData', kind: 'scalar', T: 7 /* ScalarType.FIXED32 */ },
  { no: 4, name: 'BinData', kind: 'scalar', T: 12 /* ScalarType.BYTES */ },
])

/**
 * @generated from message bfdx_proto.CommonResp
 */
export const CommonResp = /*@__PURE__*/ proto3.makeMessageType('bfdx_proto.CommonResp', () => [
  { no: 1, name: 'Code', kind: 'enum', T: proto3.getEnumType(RespCode) },
  { no: 2, name: 'RespInfo', kind: 'scalar', T: 9 /* ScalarType.STRING */ },
  { no: 3, name: 'StrData', kind: 'scalar', T: 9 /* ScalarType.STRING */ },
  { no: 4, name: 'IntData', kind: 'scalar', T: 7 /* ScalarType.FIXED32 */ },
  { no: 5, name: 'BinData', kind: 'scalar', T: 12 /* ScalarType.BYTES */ },
])

/**
 * @generated from message bfdx_proto.Gateway8100SubsInfo
 */
export const Gateway8100SubsInfo = /*@__PURE__*/ proto3.makeMessageType('bfdx_proto.Gateway8100SubsInfo', () => [
  { no: 1, name: 'DMRIDs', kind: 'scalar', T: 7 /* ScalarType.FIXED32 */, repeated: true },
])
