import { computed, ComputedRef } from 'vue'
import i18n from '@/modules/i18n'

const { t } = i18n.global

export interface NavItemConfig {
  label?: string
  order?: number // 菜单排序值
  activeIconPath?: string
  inactiveIconPath?: string
  defaultChildPath?: string // 默认子路由路径
}
const dataManageImgFolderName = 'manage'

export const toIconPath = (parent: string, name: string) => ({
  activeIconPath: `images/${parent}/${name}_selected.svg`,
  inactiveIconPath: `images/${parent}/${name}.svg`,
})

export const dataManageNavItemConfig: Record<string, ComputedRef<NavItemConfig>> = {
  Orgs: computed(() => ({
    label: t('nav.orgData'),
    order: 1,
    ...toIconPath(dataManageImgFolderName, 'org'),
  })),
  Jobs: computed(() => ({
    label: t('nav.postData'),
    order: 2,
    ...toIconPath(dataManageImgFolderName, 'job'),
  })),
  Users: computed(() => ({
    label: t('nav.userData'),
    order: 3,
    ...toIconPath(dataManageImgFolderName, 'user'),
  })),
  controllerManage: computed(() => ({
    label: t('nav.ctrlData'),
    order: 4,
    ...toIconPath(dataManageImgFolderName, 'controller'),
    defaultChildPath: 'Controllers',
  })),
  deviceManage: computed(() => ({
    label: t('dialog.deviceDataTitle'),
    order: 5,
    ...toIconPath(dataManageImgFolderName, 'device'),
    defaultChildPath: 'Devices',
  })),
  phoneManage: computed(() => ({
    label: t('nav.phoneManage'),
    order: 6,
    ...toIconPath(dataManageImgFolderName, 'phone'),
    defaultChildPath: 'GatewayFilter',
  })),
  patrolManage: computed(() => ({
    label: t('nav.patrolManage'),
    order: 7,
    ...toIconPath(dataManageImgFolderName, 'patrol'),
    defaultChildPath: 'LinePoint',
  })),
  IOT: computed(() => ({
    label: t('nav.iotManage'),
    order: 8,
    ...toIconPath(dataManageImgFolderName, 'iot'),
  })),
  otherManage: computed(() => ({
    label: t('nav.otherOperations'),
    order: 9,
    ...toIconPath(dataManageImgFolderName, 'other'),
    defaultChildPath: 'MapPoints',
  })),
}
