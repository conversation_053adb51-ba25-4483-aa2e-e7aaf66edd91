import { computed, ComputedRef } from 'vue'
import { NavItemConfig } from './dataNav'
import i18n from '@/modules/i18n'
const { t } = i18n.global

export const DispatchImgFolderName = 'dispatch'

export const dispatchNavItemConfig: Record<string, ComputedRef<NavItemConfig>> = {
  GisApplication: computed(() => ({
    label: t('nav.GisApplication'),
    order: 1,
  })),
  CommunicationDispatch: computed(() => ({
    label: t('nav.CommunicationDispatch'),
    order: 2,
  })),
  DataApplication: computed(() => ({
    label: t('nav.DataApplication'),
    order: 3,
  })),
}

export const toIconPath = (parent: string, name: string) => ({
  activeIconPath: `images/${parent}/dataApplication/${name}_selected.svg`,
  inactiveIconPath: `images/${parent}/dataApplication/${name}.svg`,
})

export const dispatchDataApplicationNavItemConfig: Record<string, ComputedRef<NavItemConfig>> = {
  soundHistory: computed(() => ({
    label: t('nav.soundHistory'),
    order: 1,
    ...toIconPath(DispatchImgFolderName, 'soundHistory'),
  })),
  alarmHistory: computed(() => ({
    label: t('nav.alarmHistory'),
    order: 2,
    ...toIconPath(DispatchImgFolderName, 'alarmHistory'),
  })),
  smsHistory: computed(() => ({
    label: t('nav.smsHistory'),
    order: 3,
    ...toIconPath(DispatchImgFolderName, 'smsHistory'),
  })),
  gpstraceHistory: computed(() => ({
    label: t('nav.GPSpathHistory'),
    order: 4,
    ...toIconPath(DispatchImgFolderName, 'gpstraceHistory'),
    fontSize: 20,
    activeFontSize: 24,
  })),
  dispatchHistory: computed(() => ({
    label: t('nav.dispatchHistory'),
    order: 5,
    ...toIconPath(DispatchImgFolderName, 'dispatchHistory'),
  })),
  crudHistory: computed(() => ({
    label: t('nav.crudHistory'),
    order: 6,
    ...toIconPath(DispatchImgFolderName, 'crudHistory'),
  })),
  onlineHistory: computed(() => ({
    label: t('nav.switchHistory'),
    order: 7,
    ...toIconPath(DispatchImgFolderName, 'onlineHistory'),
  })),
  patrolHistory: computed(() => ({
    label: t('nav.patrolHistory'),
    order: 8,
    ...toIconPath(DispatchImgFolderName, 'patrolHistory'),
    defaultChildPath: 'InsRulesHistory',
    fontSize: 20,
    activeFontSize: 24,
  })),
  iotDeviceHistory: computed(() => ({
    label: t('nav.iotDeviceHistory'),
    order: 9,
    ...toIconPath(DispatchImgFolderName, 'iotDeviceHistory'),
  })),
}
