<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :deviceRid="deviceRid"
    :userRid="userRid"
    :head="dthead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    :parse-request-data="parseRequestdata"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item prop="deviceRid">
        <template #label>
          <EllipsisText :content="$t('dialog.terminalName')" class="!w-[64px] text-right" />
        </template>
        <DataTableElSelect
          ref="deviceElSelectRef"
          v-model="deviceRid"
          filterable
          clearable
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
      <el-form-item prop="userRid">
        <template #label>
          <EllipsisText :content="$t('dialog.userName')" class="!w-[64px] text-right" />
        </template>
        <DataTableElSelect
          ref="userElSelectRef"
          v-model="userRid"
          filterable
          clearable
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
    </template>
  </historyCommon>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'

  import historyCommon from '@/components/common/historyCommon.vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import DataTableElSelect from '@/components/common/dataTable/DataTableElSelect.vue'

  export default {
    mixins: [vueMixin],
    components: {
      historyCommon,
      EllipsisText,
      DataTableElSelect,
    },
    data() {
      return {
        queryProps: {
          dbListName: 'db_device_power_onoff_list',
          cmd: 45,
        },
        dataTableName: 'onlineHistoryTable',
        deviceRid: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
      }
    },
    methods: {
      removeDataTableData() {
        this.deviceRid = ''
        this.userRid = ''
      },
      parseRequestdata(item) {
        var device = bfglob.gdevices.get(item.deviceId)
        if (typeof device === 'undefined') {
          bfglob.console.error('没有此对讲机', item.deviceId)
          return
        }
        item.orgShortName = device.orgShortName
        item.deviceSelfId = device.selfId
        item.userName = bfglob.guserData.getUserNameByKey(item.userId)

        return item
      },
    },
    computed: {
      contentClass() {
        return this.isMobile ? 'is-mobile ' : ''
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.terminalName'),
            data: 'deviceSelfId',
            width: this.isFR || this.isEN ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.userName'),
            data: 'userName',
            width: this.isFR || this.isEN ? '120px' : '100px',
          },
          {
            title: this.$t('dataTable.times'),
            data: 'actionTime',
            width: '120px',
          },
          {
            title: this.$t('dataTable.type'),
            data: null,
            width: '80px',
            render: (data, _type, _row, _meta) => {
              let actionTypeName = ''
              switch (data.actionType) {
                case 0:
                  actionTypeName = this.$t('dataTable.powerOff')
                  break
                case 1:
                  actionTypeName = this.$t('dataTable.powerOn')
                  break
                case 2:
                  actionTypeName = this.$t('dataTable.batteryOn')
                  break
                case 3:
                  actionTypeName = this.$t('dataTable.resetOn')
                  break
              }
              return actionTypeName
            },
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.switchHistory')
      },
    },
  }
</script>

<style lang="scss" scoped></style>
