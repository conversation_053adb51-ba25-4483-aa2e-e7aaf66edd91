<template>
  <history-common
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :deviceRid="deviceRid"
    :userRid="userRid"
    :parseRequestData="parseRequestData"
    :head="dthead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item prop="deviceRid">
        <template #label>
          <EllipsisText :content="$t('dialog.terminalName')" class="!w-[64px] text-right" />
        </template>
        <DataTableElSelect v-model="deviceRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
      <el-form-item prop="userRid">
        <template #label>
          <EllipsisText :content="$t('dialog.userName')" class="!w-[64px] text-right" />
        </template>
        <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
    </template>
  </history-common>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  export default {
    mixins: [vueMixin],
    components: {
      historyCommon,
      EllipsisText,
    },
    data() {
      return {
        queryProps: {
          dbListName: 'db_alarm_history_list',
          cmd: 47,
        },
        dataTableName: 'alarmHistoryTable',
        deviceRid: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
      }
    },
    methods: {
      // 报警历史，包含常规对讲机和物联终端报警
      parseRequestData(item) {
        const parseIotDeviceHistory = device => {
          const result = {
            orgShortName: '',
            deviceSelfId: '',
          }
          if (!device) {
            return result
          }

          return Object.assign({}, result, {
            orgShortName: bfglob.gorgData.getShortName(device.orgId),
            deviceSelfId: device.devName,
          })
        }
        const parseInterPhoneDeviceHistory = device => {
          const result = {
            orgShortName: '',
            deviceSelfId: '',
          }
          if (!device) {
            return result
          }

          return {
            orgShortName: bfglob.gorgData.getShortName(device.orgId),
            deviceSelfId: device.selfId,
          }
        }
        // 1：对讲机，2：工牌，其他为物联终端
        const deviceDevType = [0, 1, 2]
        let device
        // 常规终端
        if (deviceDevType.includes(item.alarmDevType)) {
          device = bfglob.gdevices.get(item.deviceId)
          Object.assign(item, parseInterPhoneDeviceHistory(device))
        } else {
          // 可能是物联终端
          device = bfglob.giotDevices.get(item.deviceId)
          Object.assign(item, parseIotDeviceHistory(device))
        }

        item.userName = bfglob.guserData.getUserNameByKey(item.personId)

        const deallerResult = JSON.parse(item.deallerResult)
        item.deallerName = deallerResult.deallerName || ''
        item.result = deallerResult.deallerResult || ''
        item.alarmConditions = deallerResult.alarmConditions || ''
        item.dealler_time = item.deallerTime === '2000-01-01 00:00:00' ? '' : item.deallerTime

        // bodyCache.push(item)
        return item
      },
      removeDataTableData() {
        this.userRid = ''
        this.deviceRid = ''
      },
    },
    computed: {
      contentClass() {
        return this.isMobile ? 'is-mobile ' : ''
      },
      alarmTypeNames() {
        return {
          // 1=紧急报警；2=强行脱网报警；3=发生欠压报警；4=GPS故障报警;5=GPS遮挡;
          // 6:防拆报警;7:节能灯人感应报警;8:节能灯声音感应报警
          1: this.$t('dataTable.emergency'),
          2: this.$t('dataTable.offNetworkAlarm'),
          3: this.$t('dataTable.lowVoltageAlarm'),
          4: this.$t('dataTable.gpsAlarm'),
          5: this.$t('dataTable.noLocationAlarm'),
          6: this.$t('dataTable.antiDismantlingAlarm'),
          7: this.$t('dataTable.infraredSensorAlarm'),
          8: this.$t('dataTable.soundSensorAlarm'),

          // 41=入界报警;42=出界报警;51=入界回岗;52=出界离岗;61=移动监控走动提示;62=移动监控停留报警
          41: this.$t('dataTable.inboundAlarm'),
          42: this.$t('dataTable.outboundsAlarm'),
          51: this.$t('dataTable.backToWorkPrompt'),
          52: this.$t('dataTable.departureAlarm'),
          61: this.$t('dataTable.walkingMobileMonitorPrompt'),
          62: this.$t('dataTable.mobileMonitoringStayAlarm'),
        }
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '120px',
          },
          {
            title: this.$t('dialog.terminalName'),
            data: 'deviceSelfId',
            width: '120px',
          },
          {
            title: this.$t('dialog.userName'),
            data: 'userName',
            width: '120px',
          },
          {
            title: this.$t('dataTable.alarmTime'),
            data: 'alarmTime',
            width: this.isFR ? '140px' : '120px',
          },
          {
            title: this.$t('dataTable.alarmType'),
            data: 'alarmType',
            width: '120px',
            render: (data, _type, _row, _meta) => {
              return this.alarmTypeNames[data] ?? data
            },
          },
          {
            title: this.$t('dataTable.alarmConditions'),
            data: 'alarmConditions',
            width: '160px',
          },
          {
            title: this.$t('dataTable.processContext'),
            data: 'result',
            width: '160px',
          },
          {
            title: this.$t('dataTable.processor'),
            data: 'deallerName',
            width: '100px',
          },
          {
            title: this.$t('dataTable.processTime'),
            data: 'dealler_time',
            width: this.isFR ? '140px' : '120px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.alarmHistory')
      },
    },
  }
</script>

<style></style>
