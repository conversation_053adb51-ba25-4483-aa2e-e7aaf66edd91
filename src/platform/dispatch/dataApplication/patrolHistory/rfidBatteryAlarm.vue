<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :user="queryProps.user"
    :point="queryProps.point"
    :pointRid="pointRid"
    :device="queryProps.device"
    :label-width="labelWidth"
    :head="dtHead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    :parse-request-data="parseRequestData"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item :label="$t('dialog.pointName')" prop="pointRid">
        <DataTableElSelect v-model="pointRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in linePointRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
    </template>
  </historyCommon>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'

  const ComponentName = 'rfidBatteryAlarm'
  export default {
    name: ComponentName,
    mixins: [vueMixin],
    data() {
      return {
        visible: false,
        queryProps: {
          dbListName: 'db_linepoint_alarm_history_list',
          cmd: 57,
          user: false,
          point: true,
          device: false,
        },
        dataTableName: 'rfidBatteryAlarm',
        pointRid: '',
        linePointRids: bfglob.glinePoints.getList(),
      }
    },
    methods: {
      removeDataTableData() {
        this.pointRid = ''
      },
      parseRequestData(item) {
        const linePoint = bfglob.glinePoints.get(item.pointId)
        if (!linePoint) {
          return
        }

        item.orgShortName = linePoint.orgShortName
        item.pointName = linePoint.pointName
        item.deviceName = ''
        item.checkerName = bfglob.guserData.getUserNameByKey(item.checkerId)

        const device = bfglob.gdevices.get(item.deviceId)
        if (device) {
          item.deviceName = device.selfId
        }

        return item
      },
    },
    components: {
      historyCommon,
    },
    computed: {
      contentClass() {
        return this.isMobile ? 'is-mobile ' : ''
      },
      labelWidth() {
        return this.isFR ? '120px' : this.isEN ? '100px' : '90px'
      },
      dtHead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '120px',
          },
          {
            title: this.$t('dialog.pointName'),
            data: 'pointName',
            width: this.isFR ? '160px' : '120px',
          },
          {
            title: this.$t('dialog.deviceName'),
            data: 'deviceName',
            width: this.isFR ? '160px' : '120px',
          },
          {
            title: this.$t('dataTable.patrolTime'),
            data: 'checkTime',
            width: this.isFR ? '140px' : '120px',
          },
          {
            title: this.$t('dataTable.inspector'),
            data: 'checkerName',
            width: '120px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.activePatrolPointAlarm')
      },
    },
  }
</script>

<style>
  .MobileLongLabel {
    .el-form-item__label {
      height: 32px;
      line-height: 1;

      @media (min-width: 768px) {
        transform: translateY(0.5rem);
      }
    }
  }
</style>
