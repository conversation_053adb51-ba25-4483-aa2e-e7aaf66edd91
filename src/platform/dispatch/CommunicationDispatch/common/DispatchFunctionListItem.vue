<template>
  <div
    class="dispatch-function-list-item-wrapper max-h-[159px] max-w-[206px] w-full h-full flex flex-col justify-around items-center cursor-pointer transition duration-100 ease-in-out"
    :class="{ selected: isSelected }"
  >
    <img :src="iconPath" class="aspect-auto max-h-[64px] max-w-[64px]" />
    <span class="item-label font-bold text-[11px] h-[38px] max-w-[120px]">
      <ellipsis-text :content="item.label" />
    </span>
  </div>
</template>

<script setup lang="ts">
  import { type Component, computed } from 'vue'

  export type FunctionListItemType = {
    label: string
    name: string
    activeIconPath?: string
    inactiveIconPath?: string
    dialogComponent?: Component
  }

  const { isSelected, item } = defineProps<{
    isSelected: boolean
    item: FunctionListItemType
  }>()

  const iconPath = computed(() => {
    return isSelected ? item.activeIconPath : item.inactiveIconPath
  })
</script>

<style scoped>
  .dispatch-function-list-item-wrapper {
    background-image: url('@/assets/images/dispatch/function_list/function_list_item.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    &.selected {
      background-image: url('@/assets/images/dispatch/function_list/function_list_item_selected.svg');
    }
  }
</style>
