<template>
  <div class="recent-contact-card-icon" :class="icon"></div>
</template>

<script lang="ts" setup>
  const props = withDefaults(
    defineProps<{
      iconClass?: string
      // 颜色，可以是rgba，linear-gradient等
      color?: string
    }>(),
    {
      iconClass: 'bfdx-duijiangjinei',
      color: '#fff',
    }
  )

  const icon = props.iconClass.includes('iconfont') ? props.iconClass : `bf-iconfont ${props.iconClass}`
</script>

<style lang="scss" scoped>
  .recent-contact-card-icon {
    background: v-bind(color);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;

    position: relative;
    width: 20px;
    height: 20px;
    line-height: 1;
    font-size: 20px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 1px solid #a7d5ff;
      background: transparent;
      color: transparent;
    }
  }
</style>
