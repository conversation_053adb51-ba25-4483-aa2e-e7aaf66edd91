<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import type { DispatchContactCardEmit } from './DispatchContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { computed, ref, useTemplateRef, watch } from 'vue'
  import { useResizeObserver } from '@vueuse/core'
  import { useI18n } from 'vue-i18n'
  import { useCommonContact } from '@/utils/callContact/commonContact'
  import openDialog from '@/utils/dialog'
  import CommonContactEdit from '../dialog/CommonContactEdit.vue'

  const { t } = useI18n()
  const { commonContacts } = useCommonContact()

  const emit = defineEmits<DispatchContactCardEmit>()
  const scrollerRef = useTemplateRef('scroller')

  watch(
    () => commonContacts.value,
    () => {
      scrollerRef.value?.updateVisibleItems(true)
    },
    { deep: true }
  )

  const itemSize = ref(calcScaleSize(132))
  const itemSecondarySize = ref(calcScaleSize(228))

  useResizeObserver(document.documentElement, () => {
    itemSize.value = calcScaleSize(132)
    itemSecondarySize.value = calcScaleSize(228)
  })

  const currentContactRids = computed<string[]>(() => {
    const currentContactRids: string[] = []
    commonContacts.value.forEach(item => {
      currentContactRids.push(item.rid)
    })
    return currentContactRids
  })

  const openCommonContactsDialog = () => {
    openDialog(CommonContactEdit, {
      currentContactRids: currentContactRids.value,
    })
  }
</script>

<template>
  <PageHeader :title="t('dispatch.commonContacts')">
    <DispatchTitleIcon icon="bfdx-bianjiyangshi2neibu" @click="openCommonContactsDialog" />
  </PageHeader>
  <RecycleScroller
    ref="scroller"
    class="contact-container"
    :items="commonContacts"
    :item-size="itemSize"
    :grid-items="5"
    :item-secondary-size="itemSecondarySize"
    key-field="dmrIDHex"
  >
    <template #default="{ item, index }">
      <DispatchContactCard
        v-if="item.dmrIDHex"
        :key="index"
        v-bind="item"
        @locate="targetDmrId => emit('locate', targetDmrId)"
        @call="targetDmrId => emit('call', targetDmrId)"
        @hangup="targetDmrId => emit('hangup', targetDmrId)"
        @message="targetDmrId => emit('message', targetDmrId)"
        @send-command="targetDmrId => emit('sendCommand', targetDmrId)"
        @send-message="targetDmrId => emit('sendMessage', targetDmrId)"
      />
    </template>
  </RecycleScroller>
</template>

<style lang="scss" scoped>
  .contact-container {
    height: 100%;
    padding: 25px 25px 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));
  }
</style>
