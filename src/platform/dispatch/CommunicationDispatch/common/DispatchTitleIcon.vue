<template>
  <div class="w-full h-[28px] flex justify-end items-center pr-[10px]">
    <span
      class="relative inline-flex justify-center items-center before:text-[9px] bg-[#0B3F67] rounded-[3px] border border-[#2CA6FF] inset-shadow-[0_0_5px_#0E9CFF] size-[18px] aspect-square cursor-pointer active:opacity-50"
      :class="iconClass"
    ></span>
  </div>
</template>

<script lang="ts" setup>
  const { icon = 'bfdx-bianjiyangshi2neibu' } = defineProps<{
    // 图标字体类名,用于iconfont图标
    icon?: string
  }>()

  const iconClass = icon.includes('iconfont') ? icon : `bf-iconfont ${icon}`
</script>
