<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import type { DynamicContact } from '@/utils/callContact'
  import type { DispatchContactCardEmit } from './DispatchContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { computed, ref, useTemplateRef, watch } from 'vue'
  import { useResizeObserver } from '@vueuse/core'
  import { useI18n } from 'vue-i18n'

  const { t } = useI18n()
  const dynamicGroupList = computed<Array<DynamicContact>>(() => [
    { rid: '1', type: 'taskGroup', dmrIDHex: '00032222', name: 'idd', parentOrg: 'test' },
    { rid: '1', type: 'tempGroup', dmrIDHex: '00032223', name: 'idd', parentOrg: 'test' },
    { rid: '1', type: 'taskGroup', dmrIDHex: '00032224', name: 'idd', parentOrg: 'test' },
    { rid: '1', type: 'taskGroup', dmrIDHex: '00032225', name: 'idd', parentOrg: 'test' },
    { rid: '1', type: 'taskGroup', dmrIDHex: '00032226', name: 'idd', parentOrg: 'test' },
    { rid: '1', type: 'taskGroup', dmrIDHex: '00032227', name: 'idd', parentOrg: 'test' },
    { rid: '1', type: 'taskGroup', dmrIDHex: '00032228', name: 'idd', parentOrg: 'test' },
    { rid: '1', type: 'taskGroup', dmrIDHex: '00032229', name: 'idd', parentOrg: 'test' },
  ])
  const itemSize = ref(calcScaleSize(132))
  const itemSecondarySize = ref(calcScaleSize(228))

  const emit = defineEmits<DispatchContactCardEmit>()

  const scrollerRef = useTemplateRef('scroller')

  watch(
    () => dynamicGroupList.value,
    () => {
      scrollerRef.value?.updateVisibleItems(true)
    },
    { deep: true }
  )

  useResizeObserver(document.documentElement, () => {
    itemSize.value = calcScaleSize(132)
    itemSecondarySize.value = calcScaleSize(228)
  })

  const openNewDynamicGroupDialog = () => {
    console.log('Opening new dynamic group dialog')
    // todo: openDialog()
  }
</script>

<template>
  <PageHeader :title="t('dispatch.dynamicGroup')">
    <DispatchTitleIcon icon="bfdx-xinzengyangshineibu" @click="openNewDynamicGroupDialog" />
  </PageHeader>
  <RecycleScroller
    ref="scroller"
    class="contact-container"
    :items="dynamicGroupList"
    :item-size="itemSize"
    :grid-items="2"
    :item-secondary-size="itemSecondarySize"
    key-field="dmrIDHex"
  >
    <template #default="{ item, index }">
      <DispatchContactCard
        v-if="item.dmrIDHex"
        :key="index"
        v-bind="item"
        @locate="targetDmrId => emit('locate', targetDmrId)"
        @call="targetDmrId => emit('call', targetDmrId)"
        @hangup="targetDmrId => emit('hangup', targetDmrId)"
        @message="targetDmrId => emit('message', targetDmrId)"
        @send-command="targetDmrId => emit('sendCommand', targetDmrId)"
        @send-message="targetDmrId => emit('sendMessage', targetDmrId)"
      />
    </template>
  </RecycleScroller>
</template>

<style lang="scss" scoped>
  .contact-container {
    height: 100%;
    padding: 20px 63px 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));
  }
</style>
