<template>
  <bf-dialog
    v-model="visible"
    ref="audioSwitch"
    :title="t('dispatch.functionList.broadcastCall')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    top="30vh"
    class="header-border shadow-md shadow-slate-800 broad-cast-full-call-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    @opened="openDlgFn"
    draggable
    center
  >
    <el-tooltip popper-class="bf-tooltip" :disabled="canSpeaking" :content="$t('msgbox.firstConnectTC918')" placement="bottom">
      <div
        class="dispatch-function-list-item-wrapper broadcast-full-call w-[310px] !h-[240px] max-w-[310px] max-h-[240px] m-auto flex flex-col justify-center items-center gap-8 transition duration-100 ease-in-out"
        :class="[speakState.current === SpeakState.SPEAKING ? 'selected' : '', canSpeaking ? 'cursor-pointer' : 'cursor-not-allowed']"
        @click="toggleSpeaking"
      >
        <img :src="iconPath" class="aspect-auto max-h-[64px] max-w-[64px]" />
        <span class="item-label font-bold text-[11px] h-[38px] max-w-[120px]">
          <ellipsis-text :content="broadcastFullCallOption.label" />
        </span>
      </div>
    </el-tooltip>
  </bf-dialog>
</template>

<script setup lang="ts">
  import bfDialog from '@/components/bfDialog/main'
  import { watch, ref, computed, onBeforeUnmount, nextTick } from 'vue'
  import { speakState, SpeakState, globalVoipServerManager, speakInfo, setupTarget } from '@/utils/speak'
  import { useI18n } from 'vue-i18n'
  import qWebChannelObj from '@/utils/qWebChannelObj'
  import broadcastFullCallSvg from '@/assets/images/dispatch/function_list/broadcast_call.svg'
  import broadcastFullCallSelectedSvg from '@/assets/images/dispatch/function_list/broadcast_full_selected.svg'

  const { t } = useI18n()

  // 接收从openDialog传递的dialogVisible属性
  const props = defineProps<{
    dialogVisible?: boolean
  }>()

  // 定义emit事件，用于更新dialogVisible
  const emit = defineEmits<{
    'update:dialogVisible': [value: boolean]
  }>()

  const broadcastFullCallOption = {
    label: t('dispatch.functionList.broadcastCall'),
    name: 'broadcastCall',
    inactiveIconPath: broadcastFullCallSvg,
    activeIconPath: broadcastFullCallSelectedSvg,
  }

  // 计算图标路径
  const iconPath = computed(() => {
    return speakState.current === SpeakState.SPEAKING ? broadcastFullCallOption.activeIconPath : broadcastFullCallOption.inactiveIconPath
  })

  // 计算是否可以通话
  const canSpeaking = computed(() => {
    const voipServer = qWebChannelObj && qWebChannelObj.server
    console.log('canSpeaking', voipServer, speakInfo.speaker, setupTarget.value, globalVoipServerManager.hasUsb, globalVoipServerManager.isConnected)
    return voipServer && speakInfo.speaker && setupTarget.value && globalVoipServerManager.hasUsb && globalVoipServerManager.isConnected
  })

  // 内部状态
  const visible = ref(false)
  const setKeysEvent = ref(false)
  const keyHandlers = ref<{ onKeyDown?: (e: KeyboardEvent) => void; onKeyUp?: (e: KeyboardEvent) => void } | null>(null)

  // 监听props.dialogVisible的变化
  watch(
    () => props.dialogVisible,
    newVal => {
      if (newVal !== undefined) {
        visible.value = newVal
      }
    },
    { immediate: true }
  )

  // 监听内部visible的变化，同步到父组件
  watch(visible, newVal => {
    emit('update:dialogVisible', newVal)
  })

  // 对话框打开时的处理函数
  const openDlgFn = () => {
    initKeysEvent()
  }

  // 切换通话状态
  const toggleSpeaking = () => {
    if (speakState.current === SpeakState.SPEAKING) {
      globalVoipServerManager.sl_i_speak_end()
    } else {
      globalVoipServerManager.sl_i_speak_start(bfglob.fullCallDmrId)
    }
  }

  // 初始化键盘事件
  const initKeysEvent = () => {
    nextTick(() => {
      if (!setKeysEvent.value) {
        setKeysEvent.value = true
        let isKeyDown = false

        const onKeyDown = (e: KeyboardEvent) => {
          if (isKeyDown) return
          isKeyDown = true

          switch (e.keyCode) {
            case 113: // F2
              if (speakState.current === SpeakState.ENDED) {
                globalVoipServerManager.sl_i_speak_start(bfglob.fullCallDmrId)
              }
              break
            case 32: // 空格
              if (document.activeElement?.nodeName === 'INPUT') return
              if (speakState.current === SpeakState.ENDED) {
                globalVoipServerManager.sl_i_speak_start(bfglob.fullCallDmrId)
              }
              break
          }
        }

        const onKeyUp = (e: KeyboardEvent) => {
          isKeyDown = false
          switch (e.keyCode) {
            case 113: // F2
            case 32: // 空格
              if (document.activeElement?.nodeName === 'INPUT') return
              if (speakState.current === SpeakState.SPEAKING) {
                globalVoipServerManager.sl_i_speak_end()
              }
              break
          }
        }

        document.addEventListener('keydown', onKeyDown)
        document.addEventListener('keyup', onKeyUp)

        // 保存事件处理器引用以便清理
        keyHandlers.value = { onKeyDown, onKeyUp }
      }
    })
  }

  // 清理键盘事件
  const cleanupKeysEvent = () => {
    if (setKeysEvent.value && keyHandlers.value) {
      document.removeEventListener('keydown', keyHandlers.value.onKeyDown!)
      document.removeEventListener('keyup', keyHandlers.value.onKeyUp!)
      keyHandlers.value = null
      setKeysEvent.value = false
    }
  }

  // 组件卸载时清理事件
  onBeforeUnmount(() => {
    cleanupKeysEvent()
  })
</script>

<style lang="scss">
  .broad-cast-full-call-dialog.el-dialog {
    width: 350px;
    height: 350px;

    .el-dialog__header {
      padding: 0;
      padding-bottom: 10px;
    }

    .broadcast-full-call {
      img {
        max-width: 76px !important;
        max-height: 100px !important;
      }
      span {
        font-size: 17px;
      }
    }
  }

  // 从 DispatchFunctionListItem 迁移的样式
  .dispatch-function-list-item-wrapper {
    background-image: url('@/assets/images/dispatch/function_list/function_list_item.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;

    &.selected {
      background-image: url('@/assets/images/dispatch/function_list/function_list_item_selected.svg');
    }
  }
</style>
