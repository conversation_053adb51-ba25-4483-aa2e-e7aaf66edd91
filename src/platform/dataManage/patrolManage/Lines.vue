<template>
  <data-form-editor
    ref="formEditor"
    class="page-lines"
    :title="pageTitle"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :detailHead="dtdetailHead"
    :detailBodyName="dataTable.detailBodyName"
    :getNewData="getNewData"
    :beforeConfirm="beforeConfirm"
    :beforeAction="beforeAction"
    :getFormRef="() => $refs.dataEditorForm"
    :show-close="true"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form ref="dataEditorForm" class="page-lines-form" :model="formData" :label-width="labelWidth" :rules="rules" label-position="left">
        <el-form-item prop="orgId">
          <template #label>
            <EllipsisText :content="$t('dialog.parentOrg') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfSelect
            v-model="formData.orgId"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :no-match-text="$t('dialog.noMatchText')"
            class="h-[50px] w-full"
          >
            <el-option v-for="item in selOrgList" :key="item.rid" :label="item.label" :value="item.rid" />
          </BfSelect>
        </el-form-item>
        <el-form-item prop="lineId">
          <template #label>
            <EllipsisText :content="$t('dialog.serialNo') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.lineId" :maxlength="16" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item prop="lineName">
          <template #label>
            <EllipsisText :content="$t('dialog.lineName') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.lineName" :maxlength="16" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item prop="note">
          <template #label>
            <EllipsisText :content="$t('dialog.notes') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.note" type="textarea" resize="none" :rows="3" :maxlength="128" class="w-full" />
        </el-form-item>
        <el-form-item prop="linePoint">
          <template #label>
            <EllipsisText :content="$t('dialog.selectPoint') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfSelect
            v-model="formData.linePoint"
            filterable
            clearable
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            @change="selLinepointChange"
            class="h-[50px] w-full"
          >
            <el-option v-for="item in selLinePoints" :key="item.rid" :label="item.label" :value="item.rid" />
          </BfSelect>
        </el-form-item>
        <el-form-item class="line-master-detail" label-width="unset">
          <template #label>
            <EllipsisText :content="$t('dialog.detailOfLineMaster') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <el-table :data="formData.detailData" border stripe :empty-text="$t('msgbox.emptyText')" :max-height="detailMaxHeight" class="lineDetail bf-table">
            <el-table-column type="index" :label="$t('dialog.index')" min-width="60" align="center" />
            <el-table-column property="pointId" :label="$t('dialog.serialNo')" min-width="65" align="center" />
            <el-table-column property="pointName" :label="$t('dialog.pointName')" min-width="100" show-overflow-tooltip align="center" />
            <el-table-column :label="$t('dialog.aheadTime')" class-name="line-detail-input-column" min-width="85" align="center">
              <template #default="scope">
                <BfInput v-model="scope.row.aheadTime" class="aheadTime !h-[32px] p-[4px]" :maxlength="4" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.delayTime')" class-name="line-detail-input-column" min-width="85" align="center">
              <template #default="scope">
                <BfInput v-model="scope.row.delayTime" class="delayTime !h-[32px] p-[4px]" :maxlength="4" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.action')" min-width="100" align="center">
              <template #default="scope">
                <el-button type="default" size="small" circle icon="caret-top" class="bg-transparent" @click="moveUp_detail_row(scope.$index)" />
                <el-button type="default" size="small" circle icon="caret-bottom" class="bg-transparent" @click="moveDn_detail_row(scope.$index)" />
                <el-button type="danger" size="small" circle icon="delete" @click="delete_detail_row(scope.$index)" />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil from '@/utils/bfutil'

  import bfNotify from '@/utils/notify'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import { cloneDeep } from 'lodash'
  import BfInput from '@/components/bfInput/main'
  import BfSelect from '@/components/bfSelect/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'

  const dbSubject = `db.${bfglob.sysId}`
  const defaultLinesData = {
    rid: '',
    orgId: bfutil.getBaseDataOrgId(),
    lineId: bfutil.getBaseSelfId(),
    lineName: '',
    oldLineName: '',
    note: '',
    detailData: [],
    detail_cache: {},
    linePoint: '',
    pointCount: 0,
  }

  export default {
    name: 'BfLines',
    mixins: [vueMixin],
    data() {
      return {
        dataTable: {
          name: 'linesTable',
          body: bfutil.objToArray(bfglob.glineMaster.getAll()),
          detailBodyName: 'pointData',
        },
        selOrgList: bfglob.gorgData.getList(),
        selLinePoints: bfglob.glinePoints.getList(),
      }
    },
    methods: {
      async onDelete(row) {
        await this.delete_linesMaster_data(row, dbCmd.DB_LINE_MASTER_DELETE)
      },
      async onUpdate(row, done) {
        const isOk = await this.updateDataFunc(row, dbCmd.DB_LINE_MASTER_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        const isOk = await this.add_linesMaster_data(row, dbCmd.DB_LINE_MASTER_INSERT)
        if (!isOk) return
        if (addNewCb) {
          const __data = this.getNewData()
          __data.orgId = row.orgId
          __data.lineId = bfutil.customNumberIncrement(row.lineId)
          // 重置标签页数据
          bfutil.resetForm(this, 'linePointDataEditorForm')
          row.detailData = []
          row.detail_cache = {}
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(defaultLinesData)
      },
      /**
       * 编辑对话框确定按钮的前置事件，返回Promise
       * @param {DataRow} row
       * @param {EditStatus} status 1:add 2:update
       * @returns {Promise<boolean>}
       */
      beforeConfirm(row, status) {
        if (row.detailData.length === 0) {
          bfNotify.messageBox(this.$t('msgbox.selLinePoint'), 'error')
          return Promise.resolve(false)
        }
        if (this.check_time_isEmpty(row.detailData)) {
          return Promise.resolve(false)
        }
        if (status === 2) {
          row.pointCount = row.detailData.length
          row.lineDetailModify = 0
        }
        return Promise.resolve(true)
      },
      /**
       * 编辑数据前置执行方法，允许拒绝编辑或添加新数据
       * @param {number} status Add: 1, Edit: 2, Delete: 3
       * @param {Record<string, any>?} row
       * @returns {Promise<boolean>}
       */
      beforeAction(status, row) {
        if (bfutil.notEditDataPermission()) {
          return Promise.reject('No permission')
        }
        if (status === 2) {
          this.transferDetailDataFromPointData(row)
        }

        return Promise.resolve(true)
      },
      transferDetailDataFromPointData(row) {
        row.oldLineName = row.lineName
        // 设置线路详情表数据
        row.detailData = []
        row.detail_cache = {}
        const pointData = Object.assign({}, row.pointData)
        for (const i in pointData) {
          // 通过 assign 解除对象的引用，避免未确定更新数据前自动更新表格数据
          const pointItem = Object.assign({}, pointData[i])
          row.detailData.push(pointItem)
          pointItem.pointCount = pointItem.pointNo = row.detailData.length
          row.detail_cache[pointItem.rid] = pointItem.rid
        }
        // 详细表按照pointNo排序
        const sortPointNo = () => {
          return row.detailData.sort(function (a, b) {
            return bfutil.sortByProps(a, b, { pointNo: 'asc' })
          })
        }
        row.detailData = sortPointNo()
      },
      add_line_detail(data, add_cmd) {
        const msgObj = {
          rid: uuid(),
          lineId: data.lineId,
          pointId: data.rid,
          pointNo: data.pointNo,
          aheadTime: parseInt(data.aheadTime),
          delayTime: parseInt(data.delayTime),
        }

        bfproto
          .sendMessage(add_cmd, msgObj, 'db_line_detail', dbSubject)
          .then(rpc_cmd_obj => {
            if (rpc_cmd_obj.resInfo === '+OK') {
              // 增加巡查线路与巡查点关系表
              bfglob.emit('add_global_db_line_detail', msgObj)
            }
          })
          .catch(err => {
            bfglob.console.warn('增加巡查线路详细表超时', err)
          })
      },
      add_linesMaster_data(data, add_cmd) {
        const msgObj = {
          rid: uuid(),
          orgId: data.orgId || '00000000-0000-0000-0000-000000000000',
          lineId: data.lineId,
          lineName: data.lineName,
          pointCount: data.pointCount,
          lineDetailModify: 0,
          note: data.note,
          detailData: data.detailData,
        }

        return bfproto
          .sendMessage(add_cmd, msgObj, 'db_line_master', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('add lineMaster res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              for (let i = 0; i < msgObj.detailData.length; i++) {
                const detail = msgObj.detailData[i]
                detail.lineId = msgObj.rid
                this.add_line_detail(detail, dbCmd.DB_LINE_DETAIL_INSERT)
              }
              bfglob.emit('add_global_lineMasterData', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.add') + msgObj.lineId + ' / ' + msgObj.lineName + this.$t('msgbox.lineData')
              bfglob.emit('addnote', note)
            } else {
              bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add lineMaster timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            return Promise.resolve(false)
          })
      },
      delete_line_detail(detail_data, del_db_cmd) {
        const msgObj = {
          rid: detail_data.lineDetailRid,
        }

        bfproto
          .sendMessage(del_db_cmd, msgObj, 'db_line_detail', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('detele lineMaster detail res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfglob.emit('delete_global_db_line_detail', detail_data)
            }
          })
          .catch(err => {
            bfglob.console.warn('delete lineMaster detail timeout:', err)
          })
      },
      update_line_detail(data, db_cmd) {
        const msgObj = {
          rid: data.lineDetailRid,
          lineId: data.lineId,
          pointId: data.rid,
          pointNo: data.pointNo,
          aheadTime: parseInt(data.aheadTime),
          delayTime: parseInt(data.delayTime),
        }

        bfproto
          .sendMessage(db_cmd, msgObj, 'db_line_detail', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('update lineMaster detail res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfglob.emit('update_global_db_line_detail', msgObj)
            }
          })
          .catch(err => {
            bfglob.console.warn('update lineMaster detail timeout:', err)
          })
      },
      update_line_detail_func(upData) {
        // 更新巡查线路详细表
        var that = this
        var __data = upData
        return new Promise((resolve, reject) => {
          try {
            // 遍历旧的详情表，如果在新的详情表中没有则删除，有则更新
            const compare_line_detail_atcion = oldItem => {
              for (const k in __data.detailData) {
                const newItem = __data.detailData[k]
                if (oldItem.lineDetailRid === newItem.lineDetailRid) {
                  return 'up'
                }
              }
              return 'del'
            }
            for (const i in __data.pointData) {
              const oldItem = __data.pointData[i]
              const action = compare_line_detail_atcion(oldItem)
              if (action === 'up') {
                // 更新以新详情表为准
              } else if (action === 'del') {
                that.delete_line_detail(oldItem, dbCmd.DB_LINE_DETAIL_DELETE)
              }
            }
            // 遍历新的详情表，如果在旧的详情表中没有则添加，有则更新
            const checked_newItem_of_add_line_detail = newItem => {
              for (const i in __data.pointData) {
                const oldItem = __data.pointData[i]
                if (oldItem.lineDetailRid === newItem.lineDetailRid) {
                  return 'up'
                }
              }
              return 'add'
            }
            for (const k in __data.detailData) {
              const newItem = __data.detailData[k]
              const action = checked_newItem_of_add_line_detail(newItem)
              if (action === 'add') {
                newItem.lineId = upData.rid
                that.add_line_detail(newItem, dbCmd.DB_LINE_DETAIL_INSERT)
              } else if (action === 'up') {
                that.update_line_detail(newItem, dbCmd.DB_LINE_DETAIL_UPDATE)
              }
            }
            resolve(1)
          } catch (err) {
            bfNotify.messageBox(this.$t('msgbox.updateLineDetailFaile'), 'error')
            reject(err)
          }
        })
      },
      update_lineMaster_data(data, up_db_cmd) {
        const msgObj = {
          ...data,
          lineDetailModify: 0,
        }

        return bfproto
          .sendMessage(up_db_cmd, msgObj, 'db_line_master', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('update lineMaster res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')

              // 更新全局组织机构数据
              bfglob.emit('update_global_lineMasterData', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.update') + msgObj.lineId + ' / ' + msgObj.lineName + this.$t('msgbox.lineData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_line_master_point_rfid_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatPointRfid'))
                return
              }
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('update lineMaster timeout', err)
            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            return Promise.resolve(false)
          })
      },
      updateDataFunc(data, db_cmd) {
        return this.update_line_detail_func(data)
          .then(_res => {
            return this.update_lineMaster_data(data, db_cmd)
          })
          .catch(err => {
            bfglob.console.error(err)
            return Promise.resolve(false)
          })
      },
      delete_linesMaster_data(data, del_cmd) {
        return bfproto
          .sendMessage(del_cmd, data, 'db_line_master', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete lineMaster res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_lineMasterData', data)

              // 添加查询日志
              const note = this.$t('dialog.delete') + data.lineId + ' / ' + data.lineName + this.$t('msgbox.lineData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_rfid_rule_master')) {
                bfNotify.warningBox(this.$t('msgbox.useInRule'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('delete lineMaster timeout', err)
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            return Promise.resolve(false)
          })
      },
      selLinepointChange(val) {
        // 遍历巡查点数据，将对应的巡查点数据 push 到线路子表中
        var linePoint = bfglob.glinePoints.get(val)
        if (!linePoint) {
          return false
        }
        const data = {
          rid: val,
          pointId: linePoint.pointId,
          pointName: linePoint.pointName,
          aheadTime: 10,
          delayTime: 10,
        }
        const process_detail_item = function (activeData) {
          // 如果缓存中已经存在巡查点的rid，则结束
          if (activeData.detail_cache[val]) {
            return false
          }
          activeData.detailData.push(data)
          activeData.pointCount = data.pointNo = activeData.detailData.length
          activeData.detail_cache[val] = val
          activeData.linePoint = ''
          return false
        }
        process_detail_item(this.$refs.formEditor.formData)
      },
      swapItems(index1, index2, stats) {
        // stats ==1 为下移，否则为上移
        var move = activeData => {
          activeData.detailData[index1] = activeData.detailData.splice(index2, 1, activeData.detailData[index1])[0]
          if (stats === 1) {
            // 下移
            activeData.detailData[index1].pointNo = index2
            activeData.detailData[index2].pointNo = index2 + 1
          } else {
            // 上移
            activeData.detailData[index1].pointNo = index1 + 1
            activeData.detailData[index2].pointNo = index1
          }
        }
        move(this.$refs.formEditor.formData)
      },
      delete_detail_row(index) {
        const delete_row = activeData => {
          const _data = activeData.detailData.splice(index, 1)
          delete activeData.detail_cache[_data[0].rid]
          activeData.pointCount--
          for (let i = index; i < activeData.detailData.length; i++) {
            const item = activeData.detailData[i]
            item.pointNo = i + 1
          }
        }
        delete_row(this.$refs.formEditor.formData)
      },
      moveUp_detail_row(index) {
        if (index === 0) {
          bfNotify.messageBox(this.$t('msgbox.alreadyFirst'), 'warning')
          return
        }
        this.swapItems(index, index - 1)
      },
      moveDn_detail_row(index) {
        const move_row = activeData => {
          if (index === activeData.detailData.length - 1) {
            bfNotify.messageBox(this.$t('msgbox.alreadyLast'), 'warning')
            return
          }
          this.swapItems(index, index + 1, 1)
        }
        move_row(this.$refs.formEditor.formData)
      },
      /**
       *  检查线路详情是否有效
       * @param {Array} dataArr detailData属性，线路详情数组
       * @returns {boolean}
       */
      check_time_isEmpty(dataArr) {
        for (let i = 0; i < dataArr.length; i++) {
          const data = dataArr[i]
          if (data.pointId === 'NA' || !data.pointId || data.pointName === 'NA' || !data.pointName) {
            bfNotify.messageBox(this.$t('dialog.index') + data.pointNo + ',' + this.$t('msgbox.notLinePoint'), 'error')
            return true
          }
          if (isNaN(data.aheadTime) || data.aheadTime < 0) {
            data.aheadTime = 10
          }
          if (isNaN(data.delayTime) || data.delayTime < 0) {
            data.delayTime = 10
          }
        }
        return false
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = bfutil.objToArray(bfglob.glineMaster.getAll())
      },
    },
    mounted() {
      bfglob.on('add_global_lineMasterData', this.upsetDataTableBody)
      bfglob.on('update_global_lineMasterData', this.upsetDataTableBody)
      bfglob.on('delete_global_lineMasterData', this.upsetDataTableBody)
    },
    components: {
      DataFormEditor,
      BfInput,
      BfSelect,
      EllipsisText,
    },
    watch: {
      fullscreen: {
        handler(newVal) {
          bfglob.emit(this.dataTable.name, newVal)
        },
      },
    },
    computed: {
      contentClass() {
        return this.isMobile ? 'is-mobile' : ''
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.serialNo'),
            data: 'lineId',
            width: '100px',
          },
          {
            title: this.$t('dialog.lineName'),
            data: 'lineName',
            width: this.isFR || this.isEN ? '125px' : '100px',
          },
          {
            title: this.$t('dialog.pointCount'),
            data: 'pointCount',
            width: this.isFR ? '145px' : this.isEN ? '100px' : '65px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '100px',
          },
        ]
      },
      dtdetailHead() {
        return [
          {
            title: this.$t('dialog.pointIndex'),
            data: 'pointNo',
          },
          {
            title: this.$t('dialog.pointSerialNo'),
            data: 'pointId',
          },
          {
            title: this.$t('dialog.pointName'),
            data: 'pointName',
          },
          {
            title: this.$t('dialog.aheadTime'),
            data: 'aheadTime',
          },
          {
            title: this.$t('dialog.delayTime'),
            data: 'delayTime',
          },
        ]
      },
      pageTitle() {
        return this.$t('dialog.lineTitle')
      },
      detailMaxHeight() {
        return this.fullscreen ? '264' : '270'
      },
      rules() {
        return {
          orgId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'change',
            },
          ],
          lineId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              max: 16,
              message: this.$t('msgbox.maxLen') + '16' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          lineName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              max: 16,
              message: this.$t('msgbox.maxLen') + '16' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          note: [
            {
              max: 128,
              message: this.$t('msgbox.maxLen') + '128' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
        }
      },
      labelWidth() {
        return convertPxToRem(calcScaleSize(100)) + 'rem'
      },
    },
  }
</script>

<style lang="scss">
  .data-form-dialog:not(.is-fullscreen) {
    .el-dialog__body:has(.line-master-detail) {
      width: 1000px;
    }

    .el-form.page-lines-form {
      max-width: 1000px;
      width: 100%;

      .el-form-item {
        max-width: 500px;
        margin: 16px auto;
      }

      .line-master-detail {
        max-width: 800px;
        width: 100%;
      }
    }
  }

  .line-master-detail {
    .el-form-item__content {
      margin-left: unset !important;
    }
  }

  .line-detail-input-column .el-input {
    height: inherit;
  }

  .line-detail-input-column .el-input__inner {
    height: inherit;
  }

  .get_lngLat_btns {
    width: 100%;
  }

  .lineDetail .el-form-item__content {
    line-height: normal;
  }

  .lineDetail.el-table td,
  .lineDetail.el-table th {
    padding: 0;
    height: 30px;
    line-height: 30px;
  }

  .lineDetail.el-table .cell {
    padding: 0;
  }

  .lineDetail.el-table .el-table__cell:first-child .cell {
    min-width: 50px;
  }

  .lineDetail.el-table .cell .el-input__inner {
    border-radius: unset;
  }

  .lineDetail.el-table .el-button.is-circle {
    padding: 5px;
  }

  /* 统一 label 高度 */
  .el-form-item__label {
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
  }
</style>
