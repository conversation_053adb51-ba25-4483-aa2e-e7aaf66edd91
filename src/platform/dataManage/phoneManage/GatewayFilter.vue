<template>
  <data-form-editor
    ref="formEditor"
    class="page-gateway-permission"
    :title="dlgTitle"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :detailHead="dtdetailHead"
    :detailBodyName="dataTable.detailBodyName"
    :detailBodyIndex="dataTable.detailBodyIndex"
    :getNewData="getNewData"
    :beforeAction="beforeAction"
    :beforeConfirm="beforeConfirm"
    :getFormRef="() => $refs.elFormRef"
    :show-close="true"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form
        ref="elFormRef"
        :model="formData"
        :label-width="blackWhiteListLabelWidth"
        label-position="left"
        :rules="rules"
        :validate-on-rule-change="false"
        :class="customClass"
      >
        <el-form-item class="black-white-list-radios-container" label-width="0" prop="intoAndOutStackRules">
          <el-radio-group v-model="formData.intoAndOutStackRules">
            <el-radio v-for="(rule, index) in intoAndOutStackRulesOption" :key="index" class="black-white-list-in-out-radio" :value="rule.value">
              <span v-text="rule.label" />
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="name">
          <template #label>
            <EllipsisText :content="$t('dialog.name') + '：'" />
          </template>
          <BfInput v-model="formData.name" class="h-[50px] w-full" />
        </el-form-item>

        <el-card
          v-for="(card, idx) in blackWhiteListConfig(formData)"
          v-show="formData.intoAndOutStackRules === card.inOrOut"
          :key="idx"
          shadow="never"
          class="bf-card"
          :class="customCardClass(idx)"
        >
          <template #header>
            <div class="black-white-list-header relative">
              <h6 class="black-white-list-title flex-item" v-text="card.title" />
              <el-form-item label-width="0px" :prop="card.enableProp" class="enable-checkbox-container">
                <BfCheckbox v-model="formData[card.enableProp]" :disabled="disabledInOrOutBlackOrWhite(formData, card)" class="h-[50px] w-full">
                  <span v-text="$t('dialog.enable')" />
                </BfCheckbox>
              </el-form-item>
            </div>
          </template>
          <el-form-item :prop="card.targetProp" label-width="0">
            <BfInput
              v-model="formData[card.targetProp]"
              :placeholder="$t('dialog.telephoneNo')"
              :maxlength="32"
              @keydown.enter="addToSpecifiedBlackWhiteList(...card.args)"
              class="h-[50px] w-full"
            >
              <template #prefix>
                <el-popover
                  popper-class="bf-tooltip"
                  :placement="phoneNoTips.placement || 'bottom'"
                  :width="phoneNoTips.width || 200"
                  :trigger="phoneNoTips.trigger || 'hover'"
                >
                  <div class="telephone-number-help center">
                    <p class="telephone-number-help-title" v-text="phoneNoTips.title || ''" />
                    <p v-for="(item, index) in phoneNoTips.contents" :key="index" class="telephone-number-help-content">
                      <span v-text="item.label" />
                      <span v-text="item.content" />
                    </p>
                  </div>
                  <template #reference>
                    <el-icon class="el-input__icon">
                      <QuestionFilled />
                    </el-icon>
                  </template>
                </el-popover>
              </template>
              <template #suffix>
                <el-icon class="el-input__icon" @click="addToSpecifiedBlackWhiteList(...card.args)">
                  <Plus />
                </el-icon>
              </template>
            </BfInput>
          </el-form-item>
          <el-form-item class="black-white-list-container" label-width="0px" :prop="card.labelProp">
            <BfCheckbox v-for="(white, idx2) in formData[card.labelProp]" :key="idx2" v-model="white.model" class="black-white-list-item h-[50px] w-full">
              <span v-text="white.value" />
            </BfCheckbox>
          </el-form-item>
          <el-button-group class="black-white-list-footer">
            <el-tooltip popper-class="bf-tooltip" effect="dark" :content="card.tips['empty'] || $t('dialog.empty')" placement="bottom">
              <el-button
                type="default"
                icon="delete"
                class="bg-transparent"
                :class="card.btnCls['empty']"
                :disabled="card.disabled['empty'] || false"
                @click="clearBlackWhiteList(...card.args)"
              />
            </el-tooltip>
            <el-tooltip popper-class="bf-tooltip" effect="dark" :content="card.tips['del'] || $t('dialog.delete')" placement="bottom">
              <el-button
                type="default"
                icon="circle-close"
                :class="card.btnCls['del']"
                class="bg-transparent"
                :disabled="card.disabled['del'] || false"
                @click="deleteBlackWhiteListItem(...card.args)"
              />
            </el-tooltip>
            <el-tooltip popper-class="bf-tooltip" effect="dark" :content="card.tips['select'] || $t('tree.selectAll')" placement="bottom">
              <el-button
                type="default"
                class="iconfont bg-transparent"
                :class="card.btnCls['select'] || 'icon-selectedAll'"
                :disabled="card.disabled['select'] || false"
                @click="selectAllBlackWhiteList(...card.args)"
              />
            </el-tooltip>
            <el-tooltip popper-class="bf-tooltip" effect="dark" :content="card.tips['move'] || $t('dialog.joinBlackList')" placement="bottom">
              <el-button
                type="default"
                class="iconfont bg-transparent"
                :class="card.btnCls['move'] || 'icon-blackList'"
                :disabled="card.disabled['move'] || false"
                @click="moveToSpecifiedBlackWhiteList(...card.args)"
              />
            </el-tooltip>
          </el-button-group>
        </el-card>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil, { getDbSubject } from '@/utils/bfutil'

  import bfNotify from '@/utils/notify'
  import bfTime from '@/utils/time'
  import validateRules from '@/utils/validateRules'
  import vueMixin from '@/utils/vueMixin'
  import { v1 as uuid } from 'uuid'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import { cloneDeep } from 'lodash'
  import BfInput from '@/components/bfInput/main'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'

  const defaultData = {
    // 以下为界面交互所需参数
    // 当前配置的是入栈/出栈规则 0:in,1:out
    intoAndOutStackRules: 0,
    inBlackList: [],
    inWhiteList: [],
    outBlackList: [],
    outWhiteList: [],
    inBlackTarget: '',
    inWhiteTarget: '',
    outBlackTarget: '',
    outWhiteTarget: '',

    // 以下为数据库需要的参数
    orgId: bfutil.getBaseDataOrgId(),
    name: '',
    note: '',
    setting: '{}',
    // 拨入黑名单
    inBlack: [],
    // 拨入黑名单是否启用
    inBlackEnable: false,
    // 拨入白名单
    inWhite: [],
    // 拨入白名单是否启用
    inWhiteEnable: false,
    // 拨出黑名单
    outBlack: [],
    // 拨出黑名单是否启用
    outBlackEnable: false,
    // 拨出白名单
    outWhite: [],
    // 拨出白名单是否启用
    outWhiteEnable: false,
  }

  export default {
    name: 'BfGatewayFilter',
    mixins: [vueMixin],
    data() {
      return {
        visible: false,
        dataTable: {
          name: 'gatewayTable',
          body: bfutil.objToArray(bfglob.gatewayFilter.getAll()),
          detailBodyName: 'detailData',
          detailBodyIndex: false,
        },

        // 黑白名单设置
        orgDataList: bfglob.gorgData.getList(),
        deviceDataList: bfglob.gdevices.getList(),
      }
    },
    methods: {
      customCardClass(index) {
        const defCls = 'black-white-list-card'
        return index === 0 ? `${defCls} first-item-node` : defCls
      },
      async onDelete(row) {
        await this.delete_gateway_filter_data(row, dbCmd.DB_PHONE_GATEWAY_FILTER_DELETE)
      },
      async onUpdate(row, done) {
        const isOk = await this.update_gateway_filter_data(row, dbCmd.DB_PHONE_GATEWAY_FILTER_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        const isOk = await this.add_gateway_filter_data(row, dbCmd.DB_PHONE_GATEWAY_FILTER_INSERT)
        if (!isOk) return
        if (addNewCb) {
          // 重置标签页数据
          bfutil.resetForm(this, 'elFormRef')
          addNewCb(row)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(defaultData)
      },
      /**
       * 编辑数据前置执行方法，允许拒绝编辑或添加新数据
       * @param {number} status Add: 1, Edit: 2, Delete: 3
       * @param {Record<string, any>?} row
       * @returns {Promise<boolean>}
       */
      beforeAction(status, _row) {
        if (bfutil.notEditDataPermission()) {
          return Promise.reject('No permission')
        }
        if (status === 2) {
          // 默认将入栈的规则显示出来
          this.transferBlackWhiteList(this.$refs.formEditor.formData)
        }

        return Promise.resolve(true)
      },
      /**
       * 编辑对话框确定按钮的前置事件，返回Promise
       * @type {beforeConfirm}
       * @param {DataRow} row
       * @param {EditStatus} status
       * @returns {Promise<boolean>}
       */
      beforeConfirm(_row, _status) {
        return this.$refs.elFormRef.validate().catch(() => {
          return Promise.reject(false)
        })
      },

      getYesOrNoLabel(getYes = true) {
        if (getYes) {
          return this.$t('dialog.yes')
        } else {
          return this.$t('dialog.no')
        }
      },
      // 黑白名单管理方法
      generateList(target) {
        return target.map(item => {
          return {
            model: false,
            value: item,
          }
        })
      },
      transferBlackWhiteList(target) {
        target.inWhiteList = this.generateList(target.inWhite)
        target.inBlackList = this.generateList(target.inBlack)
        target.outWhiteList = this.generateList(target.outWhite)
        target.outBlackList = this.generateList(target.outBlack)
      },
      // 重置黑白名单输入框
      resetInputTarget(rule, bw) {
        const process = target => {
          switch (rule) {
            case 0:
              if (bw === 0) {
                target.inBlackTarget = ''
              } else {
                target.inWhiteTarget = ''
              }
              break
            case 1:
              if (bw === 0) {
                target.outBlackTarget = ''
              } else {
                target.outWhiteTarget = ''
              }
              break
          }
        }
        process(this.$refs.formEditor.formData)
      },
      // 获取要添加数据的目标对象和互斥对象目标
      getBlackWhiteListTargets(rule, bw) {
        // rule 0:in 1:out
        // bw 0:blackList 1:whiteList
        const response = {}
        const process = target => {
          switch (rule) {
            case 0:
              if (bw === 0) {
                response.target = target.inBlackTarget
                response.insertTarget = target.inBlackList
                response.deleteTarget = target.inWhiteList
              } else {
                response.target = target.inWhiteTarget
                response.insertTarget = target.inWhiteList
                response.deleteTarget = target.inBlackList
              }
              break
            case 1:
              if (bw === 0) {
                response.target = target.outBlackTarget
                response.insertTarget = target.outBlackList
                response.deleteTarget = target.outWhiteList
              } else {
                response.target = target.outWhiteTarget
                response.insertTarget = target.outWhiteList
                response.deleteTarget = target.outBlackList
              }
              break
          }
        }

        process(this.$refs.formEditor.formData)

        return response
      },
      // 判断目标对象中是否有指定的数据
      includesTarget(originTarget, target) {
        for (let i = 0; i < originTarget.length; i++) {
          const item = originTarget[i]
          if (item.value === target) {
            return true
          }
        }
        return false
      },
      // 添加一个名单到对应黑名单列表
      addOneItemToBlackWhiteList(targets) {
        const { target, deleteTarget, insertTarget } = targets
        // 删除指定目标中对应的数据
        for (let i = 0; i < deleteTarget.length; i++) {
          const item = deleteTarget[i]
          if (item.value === target) {
            deleteTarget.splice(i, 1)
            break
          }
        }

        // 再添加数据，需要先判断是否已经存在
        if (!this.includesTarget(insertTarget, target)) {
          insertTarget[insertTarget.length] = {
            model: false,
            value: target,
          }
        }
      },
      // 验证输入的电话号码
      getNeedValidateFileName(rule, bw) {
        const getPropName = () => {
          let name = 'inBlackTarget'

          switch (rule) {
            case 0:
              if (bw === 0) {
                name = 'inBlackTarget'
              } else {
                name = 'inWhiteTarget'
              }
              break
            case 1:
              if (bw === 0) {
                name = 'outBlackTarget'
              } else {
                name = 'outWhiteTarget'
              }
              break
          }

          return name
        }

        const res = {}
        res.refName = 'elFormRef'
        res.propName = getPropName()

        return res
      },
      validateInputPhoneNo(rule, bw) {
        return new Promise((resolve, reject) => {
          const res = this.getNeedValidateFileName(rule, bw)
          if (!this.$refs[res.refName] || typeof this.$refs[res.refName].validateField !== 'function') {
            reject(false)
            return
          }
          this.$refs[res.refName].validateField(res.propName, valid => {
            if (valid) {
              return resolve(true)
            }

            reject(false)
          })
        })
      },
      // 添加对应黑白名单数据逻辑
      addToSpecifiedBlackWhiteList(rule, bw) {
        const targets = this.getBlackWhiteListTargets(rule, bw)

        // 输入的号码要先验证
        this.validateInputPhoneNo(rule, bw)
          .then(_valid => {
            this.addOneItemToBlackWhiteList(targets)
            this.resetInputTarget(rule, bw)
          })
          .catch(() => {})
      },
      // 清空对应的黑白名单
      clearBlackWhiteList(rule, bw) {
        const process = target => {
          switch (rule) {
            case 0:
              if (bw === 0) {
                target.inBlackList = []
              } else {
                target.inWhiteList = []
              }
              break
            case 1:
              if (bw === 0) {
                target.outBlackList = []
              } else {
                target.outWhiteList = []
              }
              break
          }
        }
        process(this.$refs.formEditor.formData)
      },
      // 将选中的名单添加到对应的黑白名单列表中
      moveToSpecifiedBlackWhiteList(rule, bw) {
        const targets = {}
        const process = target => {
          // 找到选中的名单
          switch (rule) {
            case 0:
              if (bw === 0) {
                targets.origin = target.inBlackList
                targets.target = target.inWhiteList
              } else {
                targets.origin = target.inWhiteList
                targets.target = target.inBlackList
              }
              break
            case 1:
              if (bw === 0) {
                targets.origin = target.outBlackList
                targets.target = target.outWhiteList
              } else {
                targets.origin = target.outWhiteList
                targets.target = target.outBlackList
              }
              break
          }
          const selectedTargetList = this.getSelectedTarget(targets.origin, true)
          // 遍历选中的名单，将每个都移动到目标列表中
          selectedTargetList.map(target => {
            // target, deleteTarget, insertTarget
            this.addOneItemToBlackWhiteList({
              target: target.value,
              deleteTarget: targets.origin,
              insertTarget: targets.target,
            })
          })
        }

        process(this.$refs.formEditor.formData)
      },
      // 查找到目标列表中选中的数据
      getSelectedTarget(target, state = true) {
        return target.filter(item => {
          return item.model === state
        })
      },
      // 删除目标列表中选中的数据
      deleteSelectedTarget(target) {
        target = this.getSelectedTarget(target, false)
        return target
      },
      // 删除选中目标数据方法
      deleteBlackWhiteListItem(rule, bw) {
        let deleteTarget = []
        let delTargetName = 'inBlackList'
        const process = target => {
          switch (rule) {
            case 0:
              if (bw === 0) {
                deleteTarget = target.inBlackList
                delTargetName = 'inBlackList'
              } else {
                deleteTarget = target.inWhiteList
                delTargetName = 'inWhiteList'
              }
              break
            case 1:
              if (bw === 0) {
                deleteTarget = target.outBlackList
                delTargetName = 'outBlackList'
              } else {
                deleteTarget = target.outWhiteList
                delTargetName = 'outWhiteList'
              }
              break
          }

          target[delTargetName] = this.deleteSelectedTarget(deleteTarget)
        }

        process(this.$refs.formEditor.formData)
      },
      // 将目标列表对象全部设置选中状态
      selectAllTargetList(targetList) {
        for (let i = 0; i < targetList.length; i++) {
          const item = targetList[i]
          item.model = true
        }

        return targetList
      },
      // 全选黑白名单列表
      selectAllBlackWhiteList(rule, bw) {
        let targetList = []
        let targetListName = 'inBlackList'
        const process = target => {
          switch (rule) {
            case 0:
              if (bw === 0) {
                targetList = target.inBlackList
                targetListName = 'inBlackList'
              } else {
                targetList = target.inWhiteList
                targetListName = 'inWhiteList'
              }
              break
            case 1:
              if (bw === 0) {
                targetList = target.outBlackList
                targetListName = 'outBlackList'
              } else {
                targetList = target.outWhiteList
                targetListName = 'outWhiteList'
              }
              break
          }

          target[targetListName] = this.selectAllTargetList(targetList)
        }

        process(this.$refs.formEditor.formData)
      },

      // 最后确定黑白名单方法
      add_gateway_filter_data(data, db_cmd) {
        this.processTargetList()
        const msgObj = {
          ...data,
          rid: uuid(),
          orgId: data.orgId || bfutil.getBaseDataOrgId() || bfutil.DefOrgRid,
          lastModifyTime: bfTime.nowUtcTime(),
          inBlack: JSON.stringify(data.inBlack),
          inWhite: JSON.stringify(data.inWhite),
          outBlack: JSON.stringify(data.outBlack),
          outWhite: JSON.stringify(data.outWhite),
          note: '',
          setting: '{}',
        }

        return bfproto
          .sendMessage(db_cmd, msgObj, 'db_phone_gateway_filter', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('add db_phone_gateway_filter res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              this.showAddSuccessMsg()
              bfglob.emit('add_global_db_phone_gateway_filter', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.add') + msgObj.name + this.$t('dialog.phoneBlackWhiteList')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_phone_gateway_filter_name_key')) {
                bfNotify.warningBox(this.$t('msgbox.nameCannotRepeated'))
              } else {
                this.showAddErrorMsg()
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add db_phone_gateway_filter timeout:', err)
            this.showAddErrorMsg()
            return Promise.resolve(false)
          })
      },
      update_gateway_filter_data(data, db_cmd) {
        this.processTargetList()
        const msgObj = {
          ...data,
          lastModifyTime: bfTime.nowUtcTime(),
          inBlack: JSON.stringify(data.inBlack),
          inWhite: JSON.stringify(data.inWhite),
          outBlack: JSON.stringify(data.outBlack),
          outWhite: JSON.stringify(data.outWhite),
          setting: data.setting || '{}',
        }
        const oldData = bfglob.gatewayFilter.get(data.rid)

        return bfproto
          .sendMessage(db_cmd, msgObj, 'db_phone_gateway_filter', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('update db_phone_gateway_filter res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              this.showUpdateSuccessMsg()
              bfglob.emit('update_global_db_phone_gateway_filter', msgObj)

              // 添加查询日志
              let targetStr = msgObj.name
              if (oldData.name) {
                targetStr = oldData.name + ' / ' + targetStr
              }
              const note = this.$t('dialog.update') + targetStr + this.$t('dialog.phoneBlackWhiteList')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_phone_gateway_filter_name_key')) {
                bfNotify.warningBox(this.$t('msgbox.nameCannotRepeated'))
                return
              }
              this.showUpdateErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('update db_phone_gateway_filter timeout:', err)
            this.showUpdateErrorMsg()
            return Promise.resolve(false)
          })
      },
      delete_gateway_filter_data(data, db_cmd) {
        this.processTargetList()
        const msgObj = {
          ...data,
          lastModifyTime: bfTime.nowUtcTime(),
          inBlack: JSON.stringify(data.inBlack),
          inWhite: JSON.stringify(data.inWhite),
          outBlack: JSON.stringify(data.outBlack),
          outWhite: JSON.stringify(data.outWhite),
        }

        bfproto
          .sendMessage(db_cmd, msgObj, 'db_phone_gateway_filter', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete db_phone_gateway_filter res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              this.showDeleteSuccessMsg()
              bfglob.emit('delete_global_db_phone_gateway_filter', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.delete') + msgObj.name + this.$t('dialog.phoneBlackWhiteList')
              bfglob.emit('addnote', note)
            } else {
              this.showDeleteErrorMsg()
            }
          })
          .catch(err => {
            bfglob.console.warn('delete db_phone_gateway_filter timeout:', err)
            this.showDeleteErrorMsg()
          })
      },
      getTargetList(target) {
        return target.map(item => {
          return item.value
        })
      },
      // 处理所有列表，将对应的value放到对应的数组中
      processTargetList() {
        const target = this.$refs.formEditor.formData
        target['inBlack'] = this.getTargetList(target.inBlackList)
        target['inWhite'] = this.getTargetList(target.inWhiteList)
        target['outBlack'] = this.getTargetList(target.outBlackList)
        target['outWhite'] = this.getTargetList(target.outWhiteList)
      },
      blackWhiteListConfig(data) {
        // inOrOut 0:in 1:out
        // args:[rule,bw]
        // rule 0:in 1:out
        // bw 0:blackList 1:whiteList
        return [
          {
            inOrOut: 0,
            inOutTitle: this.$t('dialog.dialInList'),
            args: [0, 1],
            disabled: {
              empty: this.disClearInWhiteList1(data) || false,
              del: this.disDelInWhiteList1(data) || false,
              select: this.disClearInWhiteList1(data) || false,
              move: this.disDelInWhiteList1(data) || false,
            },
            btnCls: {
              move: ' icon-blackList',
            },
            tips: {
              move: this.$t('dialog.joinBlackList'),
            },
            title: this.$t('dialog.whiteList'),
            enableProp: 'inWhiteEnable',
            labelProp: 'inWhiteList',
            targetProp: 'inWhiteTarget',
          },
          {
            inOrOut: 0,
            inOutTitle: this.$t('dialog.dialInList'),
            args: [0, 0],
            disabled: {
              empty: this.disClearInBlackList1(data) || false,
              del: this.disDelInBlackList1(data) || false,
              select: this.disClearInBlackList1(data) || false,
              move: this.disDelInBlackList1(data) || false,
            },
            btnCls: {
              move: ' icon-whiteList',
            },
            tips: {
              move: this.$t('dialog.joinWhiteList'),
            },
            title: this.$t('dialog.blackList'),
            enableProp: 'inBlackEnable',
            labelProp: 'inBlackList',
            targetProp: 'inBlackTarget',
          },
          {
            inOrOut: 1,
            inOutTitle: this.$t('dialog.dialOutList'),
            args: [1, 1],
            disabled: {
              empty: this.disClearOutWhiteList1(data) || false,
              del: this.disDelOutWhiteList1(data) || false,
              select: this.disClearOutWhiteList1(data) || false,
              move: this.disDelOutWhiteList1(data) || false,
            },
            btnCls: {
              move: ' icon-blackList',
            },
            tips: {
              move: this.$t('dialog.joinBlackList'),
            },
            title: this.$t('dialog.whiteList'),
            enableProp: 'outWhiteEnable',
            labelProp: 'outWhiteList',
            targetProp: 'outWhiteTarget',
          },
          {
            inOrOut: 1,
            inOutTitle: this.$t('dialog.dialOutList'),
            args: [1, 0],
            disabled: {
              empty: this.disClearOutBlackList1(data) || false,
              del: this.disDelOutBlackList1(data) || false,
              select: this.disClearOutBlackList1(data) || false,
              move: this.disDelOutBlackList1(data) || false,
            },
            btnCls: {
              move: ' icon-whiteList',
            },
            tips: {
              move: this.$t('dialog.joinWhiteList'),
            },
            title: this.$t('dialog.blackList'),
            enableProp: 'outBlackEnable',
            labelProp: 'outBlackList',
            targetProp: 'outBlackTarget',
          },
        ]
      },

      // 清空按钮禁用计算属性
      disClearInBlackList1(data) {
        return !data.inBlackList || data.inBlackList.length === 0
      },
      disClearInWhiteList1(data) {
        return !data.inWhiteList || data.inWhiteList.length === 0
      },
      disClearOutBlackList1(data) {
        return !data.outBlackList || data.outBlackList.length === 0
      },
      disClearOutWhiteList1(data) {
        return !data.outWhiteList || data.outWhiteList.length === 0
      },
      // 禁用删除/移动选中目标按钮计算属性
      disDelInBlackList1(data) {
        return (
          !data.inBlackList ||
          data.inBlackList.filter(item => {
            return item.model === true
          }).length === 0
        )
      },
      disDelInWhiteList1(data) {
        return (
          !data.inWhiteList ||
          data.inWhiteList.filter(item => {
            return item.model === true
          }).length === 0
        )
      },
      disDelOutBlackList1(data) {
        return (
          !data.outBlackList ||
          data.outBlackList.filter(item => {
            return item.model === true
          }).length === 0
        )
      },
      disDelOutWhiteList1(data) {
        return (
          !data.outWhiteList ||
          data.outWhiteList.filter(item => {
            return item.model === true
          }).length === 0
        )
      },
      disabledInOrOutBlackOrWhite(data, card) {
        return !data[card.labelProp] || data[card.labelProp].length === 0
      },
      //在mounted中使用watch监视子组件数据
      watchListAndSetEnable() {
        this.$watch(
          () => this.$refs.formEditor.formData.inWhiteList,
          newVal => {
            if (!newVal || newVal.length === 0) {
              this.$refs.formEditor.formData.inWhiteEnable = false
            }
          },
          {
            deep: true,
            immediate: true,
          }
        )
        this.$watch(
          () => this.$refs.formEditor.formData.inBlackList,
          newVal => {
            if (!newVal || newVal.length === 0) {
              this.$refs.formEditor.formData.inBlackEnable = false
            }
          },
          {
            deep: true,
            immediate: true,
          }
        )
        this.$watch(
          () => this.$refs.formEditor.formData.outWhiteList,
          newVal => {
            if (!newVal || newVal.length === 0) {
              this.$refs.formEditor.formData.outWhiteEnable = false
            }
          },
          {
            deep: true,
            immediate: true,
          }
        )
        this.$watch(
          () => this.$refs.formEditor.formData.outBlackList,
          newVal => {
            if (!newVal || newVal.length === 0) {
              this.$refs.formEditor.formData.outBlackEnable = false
            }
          },
          {
            deep: true,
            immediate: true,
          }
        )
      },

      // 监听数据操作变化，以同步dataTable数据源
      add_global_phone_gateway_filter(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gatewayFilter.getAll())
      },
      update_global_phone_gateway_filter(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gatewayFilter.getAll())
      },
      delete_global_phone_gateway_filter(_data) {
        this.dataTable.body = bfutil.objToArray(bfglob.gatewayFilter.getAll())
      },
    },
    mounted() {
      bfglob.on('add_global_phone_gateway_filter', this.add_global_phone_gateway_filter)
      bfglob.on('update_global_phone_gateway_filter', this.update_global_phone_gateway_filter)
      bfglob.on('delete_global_phone_gateway_filter', this.delete_global_phone_gateway_filter)
      this.watchListAndSetEnable()
    },
    components: {
      DataFormEditor,
      BfInput,
      BfCheckbox,
      EllipsisText,
    },
    computed: {
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.name'),
            data: 'name',
            width: '100px',
          },
          {
            title: this.$t('dialog.enableStackingBlacklist'),
            data: null,
            width: '120px',
            render: (_data, _type, row, _meta) => {
              return this.getYesOrNoLabel(row.inBlackEnable)
            },
          },
          {
            title: this.$t('dialog.enableStackingWhitelist'),
            data: null,
            width: '120px',
            render: (_data, _type, row, _meta) => {
              return this.getYesOrNoLabel(row.inWhiteEnable)
            },
          },
          {
            title: this.$t('dialog.enablePopUpBlacklist'),
            data: null,
            width: '120px',
            render: (_data, _type, row, _meta) => {
              return this.getYesOrNoLabel(row.outBlackEnable)
            },
          },
          {
            title: this.$t('dialog.enablePopUpWhitelist'),
            data: null,
            width: '120px',
            render: (_data, _type, row, _meta) => {
              return this.getYesOrNoLabel(row.outWhiteEnable)
            },
          },
        ]
      },
      dtdetailHead() {
        return [
          {
            title: this.$t('dialog.stackingBlacklist'),
            data: null,
            render: (_data, _type, row, _meta) => {
              return row.inBlack.join(',')
            },
          },
          {
            title: this.$t('dialog.stackingWhitelist'),
            data: null,
            render: (_data, _type, row, _meta) => {
              return row.inWhite.join(',')
            },
          },
          {
            title: this.$t('dialog.popUpBlacklist'),
            data: null,
            render: (_data, _type, row, _meta) => {
              return row.outBlack.join(',')
            },
          },
          {
            title: this.$t('dialog.popUpWhitelist'),
            data: null,
            render: (_data, _type, row, _meta) => {
              return row.outWhite.join(',')
            },
          },
        ]
      },
      dlgTitle() {
        return this.$t('dialog.phoneBlackWhiteList')
      },
      rules() {
        return {
          orgId: [validateRules.required(['change', 'blur'])],
          name: [validateRules.required('blur'), validateRules.maxLen('blur', 16)],
          inBlackTarget: [validateRules.blackWhiteListRule()],
          inWhiteTarget: [validateRules.blackWhiteListRule()],
          outBlackTarget: [validateRules.blackWhiteListRule()],
          outWhiteTarget: [validateRules.blackWhiteListRule()],
        }
      },

      disConfirmBtn() {
        return false
      },
      intoAndOutStackRulesOption() {
        return [
          {
            value: 0,
            label: this.$t('dialog.dialInList'),
          },
          {
            value: 1,
            label: this.$t('dialog.dialOutList'),
          },
        ]
      },
      blackWhiteListLabelWidth() {
        return convertPxToRem(calcScaleSize(100)) + 'rem'
      },
      phoneNoTips() {
        return {
          placement: 'left',
          width: '260',
          title: this.$t('dialog.phoneNoInputHelp'),
          contents: [
            {
              label: this.$t('msgbox.example') + ': ',
              content: '*123; 123*; *123*; ?123; 123?; ?123?; 123*456; 13012341234',
            },
            {
              label: this.$t('msgbox.symbolDescription') + ' "*": ',
              content: this.$t('msgbox.matchAnyNumber'),
            },
            {
              label: this.$t('msgbox.symbolDescription') + ' "?": ',
              content: this.$t('msgbox.matchArbitraryNo'),
            },
          ],
        }
      },
      customClass() {
        const defCls = 'gatewayRule grid grid-cols-2 gap-2 '
        const mobileCls = 'gatewayRule grid grid-cols-1 '
        return this.isMobile ? `${mobileCls} is-mobile` : defCls
      },
    },
  }
</script>

<style lang="scss">
  .gatewayRule .el-collapse-item__header {
    height: 32px;
    line-height: 32px;
  }

  .gatewayRule .el-collapse-item__content {
    padding-bottom: 10px;
  }

  .el-form-item.form-item-notes {
    margin-top: 8px;
  }

  .black-white-list-card {
    display: flex;
    flex-direction: column;
    flex-grow: 0;
  }

  .is-mobile .black-white-list-card {
    width: auto;
    margin-top: 15px;
  }

  .is-mobile .black-white-list-card.first-item-node {
    margin-top: 0;
  }

  .is-mobile.el-form .el-form-item.black-white-list-radios-container {
    margin-bottom: 8px;
  }

  .el-form.gatewayRule .black-white-list-card .el-card__body {
    flex: auto;
    display: flex;
    flex-direction: column;
    padding: 6px 10px;

    .el-form-item {
      margin-bottom: 8px;
    }
  }

  .black-white-list-container,
  .black-white-list-footer {
    flex: auto;
  }

  .black-white-list-container {
    overflow: auto;
    max-height: 40vh;
    min-height: 20vh;

    & > .el-form-item__content {
      display: block;

      .black-white-list-item {
        line-height: 24px;
        height: 24px;
        margin: 0;
      }
    }
  }

  .black-white-list-footer {
    flex-grow: 0;
    margin-left: auto;
    margin-right: auto;
  }

  .black-white-list-footer .el-button {
    padding: 4px 8px;
    font-size: 20px;
    line-height: 20px;
  }

  .black-white-list-card .el-card__header {
    padding: 4px 8px;
    text-align: center;
  }

  .black-white-list-card .el-card__header .el-checkbox {
    line-height: 28px;
  }

  .black-white-list-title,
  .black-white-list-header .el-form-item__content {
    margin: 0;
    line-height: 28px;
  }

  .black-white-list-header .enable-checkbox-container {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
  }

  .enable-checkbox-container .el-form-item__content {
    display: flex;
    align-self: center;
  }

  .enable-stack-rules-wrap {
    flex-wrap: wrap;
  }

  .black-and-white-list .no-margin-x {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .gatewayRule .telephone-number-help-content {
    line-height: 28px;
    padding: 0;
    margin: 0;
  }

  /* 统一 label 高度 */
  .el-form-item__label {
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
  }
</style>
