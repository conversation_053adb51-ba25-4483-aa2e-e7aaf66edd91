import screenfull from 'screenfull'
import i18n from '@/modules/i18n'
import bfNotify from '@/utils/notify'
import bfStorage from '@/utils/storage'
import bfTime from '@/utils/time'
import { destination } from '@turf/turf'
import { h } from 'vue'

export function isPC() {
  // 检测浏览器是否是移动端
  var userAgent = navigator.userAgent.toLowerCase()
  var isPC = userAgent.match(/(iPhone|iPod|Android|ios|iPad|Mobile)/i) === null

  return isPC
}

export function getLayoutLevel() {
  let dev_width = window.innerWidth
  if (dev_width >= 1900) {
    return 4
  } else if (dev_width >= 1200) {
    return 3
  } else if (dev_width >= 992) {
    return 2
  } else if (dev_width >= 768) {
    return 1
  } else {
    return 0
  }
}

export function notSendCmdPermission() {
  if (!bfglob.userInfo.setting.sendCommand) {
    bfNotify.warningBox(i18n.global.t('msgbox.notSendCmd'))
    return true
  }
  return false
}

export function notEditDataPermission() {
  if (!bfglob.userInfo.setting.editData) {
    bfNotify.warningBox(i18n.global.t('msgbox.notEdit'), 'warning')
    return true
  }
  return false
}

export function notEditUserPermDataPermission() {
  if (!bfglob.userInfo.setting.editUserPerm) {
    bfNotify.warningBox(i18n.global.t('msgbox.notEditUserPerm'), 'warning')
    return true
  }
  return false
}

export function notDeleteDataPermission(deleteOrgId) {
  const org = bfglob.gorgData.get(deleteOrgId)
  if (!org) {
    bfNotify.warningBox(i18n.global.t('msgbox.delOutsidePermissions'), 'warning')
    return true
  }
  return false
}

export function notRepeaterWfPermission() {
  if (!bfglob.userInfo.setting.repeaterWf) {
    bfNotify.warningBox(i18n.global.t('msgbox.notRepeaterWf'), 'warning')
    return true
  }
  return false
}

export function notIntercomWfPermission() {
  if (!bfglob.userInfo.setting.intercomWf) {
    bfNotify.warningBox(i18n.global.t('msgbox.notIntercomWf'), 'warning')
    return true
  }
  return false
}

export function notDynamicGroupPermission() {
  if (!bfglob.userInfo.setting.dynamicGroupPermission) {
    bfNotify.warningBox(i18n.global.t('msgbox.notDynamicGroupPerm'), 'warning')
    return true
  }

  return false
}

export const RootId = '00000000-0000-0000-0000-000000000000'
export const DefOrgRid = '00000000-0000-0000-0000-000000000000'
export const ImageRid = '22222222-2222-2222-2222-222222222222'

export function getDbSubject() {
  return `db.${bfglob.sysId}`
}

export function encodeStringIp(ipv4) {
  if (typeof ipv4 !== 'string') {
    return 0
  }

  const arr = ipv4.split('.')
  const tmp = (arr[0] << 24) | (arr[1] << 16) | (arr[2] << 8) | arr[3]
  return tmp >>> 0
}

export function decodeInt32Ip(int32Ip) {
  if (typeof int32Ip !== 'number' || int32Ip < 0) {
    return ''
  }

  return (int32Ip >>> 24) + '.' + ((int32Ip >> 16) & 255) + '.' + ((int32Ip >> 8) & 255) + '.' + (int32Ip & 255)
}

export function frequencyHz2Mhz(hz) {
  let mhz = ''

  // 如果不是数字，尝试转换为数字类型
  if (typeof hz !== 'number') {
    hz = parseFloat(hz)
    if (isNaN(hz)) {
      return mhz
    }
  }

  // 如果是0,则返回空字符串
  if (hz === 0) {
    return mhz
  }

  // 如果小于0,则取绝对值
  if (hz < 0) {
    hz = Math.abs(hz)
  }

  mhz = (hz / 1000 / 1000 + '').slice(0, 10)

  return mhz
}

export function frequencyMhz2Hz(mhz) {
  let hz = 0

  // 非数字或字符串，则返回0
  const isString = typeof mhz === 'string'
  const isNumber = typeof mhz === 'number'

  if (!isString && !isNumber) {
    return hz
  }

  // 如果是字符串，则尝试转换为数字
  if (isString) {
    mhz = parseFloat(mhz)
    if (isNaN(mhz)) {
      return hz
    }
  }

  hz = mhz * 1000 * 1000

  return Math.round(hz)
}

export function sortByProps(item1, item2) {
  'use strict'
  var props = []
  for (var _i = 2; _i < arguments.length; _i++) {
    props[_i - 2] = arguments[_i]
  }
  var cps = [] // 存储排序属性比较结果。
  // 如果未指定排序属性，则按照全属性升序排序。
  var asc = true
  if (props.length < 1) {
    for (var p in item1) {
      if (item1[p] > item2[p]) {
        cps.push(1)
        break // 大于时跳出循环。
      } else if (item1[p] === item2[p]) {
        cps.push(0)
      } else {
        cps.push(-1)
        break // 小于时跳出循环。
      }
    }
  } else {
    for (var i = 0; i < props.length; i++) {
      var prop = props[i]
      for (var o in prop) {
        asc = prop[o] === 'asc'
        if (item1[o] > item2[o]) {
          cps.push(asc ? 1 : -1)
          break // 大于时跳出循环。
        } else if (item1[o] === item2[o]) {
          cps.push(0)
        } else {
          cps.push(asc ? -1 : 1)
          break // 小于时跳出循环。
        }
      }
    }
  }
  for (var j = 0; j < cps.length; j++) {
    if (cps[j] === 1 || cps[j] === -1) {
      return cps[j]
    }
  }
  return 0
}

export function getActiveDevices(rfidId) {
  const linePoint = bfglob.glinePoints.getDataByIndex(rfidId)
  if (!linePoint) {
    return
  }

  if (!linePoint.activeDevices) {
    linePoint.activeDevices = new Set()
  }

  return linePoint.activeDevices
}

export function getMarkerByIndex(index) {
  const linePoint = bfglob.glinePoints.getDataByIndex(index)
  if (!linePoint) {
    return
  }
  return bfglob.glinePoints.getMarker(linePoint.rid)
}

export function getRfidDevices(rfidId) {
  const devices = bfglob.gdevices.getAll()
  return Object.keys(devices)
    .filter(key => {
      return devices[key].lastRfid === rfidId
    })
    .map(key => devices[key])
}

export function getDeviceMapLonLat(data) {
  // 固定位置的终端类型列表，devices.vue计算属性
  const fixedDevices = [2, 3, 6, 7, 21]
  const defaultLonLat = [data.lastLon, data.lastLat]
  if (fixedDevices.includes(data.deviceType)) {
    const devLonLat = JSON.parse(data.setting).lonLat
    return devLonLat ? [devLonLat.lon, devLonLat.lat] : defaultLonLat
  }

  return defaultLonLat
}

// 空操作方法，一般于Vue Props中使用
export function noop() {
  /* null */
}

export function objToArray(obj) {
  if (typeof obj !== 'object') {
    bfglob.console.warn('param must is object')
    return obj
  }
  const arr = []
  for (const key in obj) {
    arr.push(obj[key])
  }
  return arr
}

export function toHexDmrId(intDmrId, is_group) {
  let hexStr = intDmrId.toString(16).padStart(6, '0')

  const prefixStr = is_group ? '80' : '00'
  hexStr = prefixStr + hexStr
  hexStr = hexStr.toUpperCase()

  return hexStr
}

export function hex2bin(hex) {
  var bytes = []
  for (var i = 0; i < hex.length - 1; i += 2) {
    bytes.push(parseInt(hex.substr(i, 2), 16))
  }
  return bytes
}

export function bin2hex(arr = [], upperCase = true) {
  let hex = ''
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    hex += item.toString(16).padStart(2, '0')
  }
  if (upperCase) {
    return hex.toUpperCase()
  }

  return hex
}

export function dmrid2ssmmm(hex_dmr_id) {
  var ssbbccmm = {
    pp: 0,
    ss: 0,
    mmm: 0,
    ok: false,
  }

  if (hex_dmr_id.length !== 8) {
    return ssbbccmm
  }

  var bin_dmr_id = hex2bin(hex_dmr_id)
  ssbbccmm.pp = bin_dmr_id[0] & 0x7f
  ssbbccmm.ss = (bin_dmr_id[1] & 0xf8) >> 3

  ssbbccmm.mmm = (bin_dmr_id[1] & 0x7) * 256 * 256 + bin_dmr_id[2] * 256 + bin_dmr_id[3]
  ssbbccmm.ok = true

  return ssbbccmm
}

export function dmrid2ssbbccmm(hex_dmr_id) {
  var ssbbccmm = {
    pp: 0,
    ss: 0,
    bb: 0,
    cc: 0,
    mm: 0,
    ok: false,
  }

  if (hex_dmr_id.length !== 8) {
    return ssbbccmm
  }

  var bin_dmr_id = hex2bin(hex_dmr_id)
  ssbbccmm.pp = bin_dmr_id[0] & 0x7f
  ssbbccmm.ss = (bin_dmr_id[1] & 0xf8) >> 3
  ssbbccmm.bb = ((bin_dmr_id[1] & 0x07) << 2) | ((bin_dmr_id[2] & 0xc0) >> 6)
  ssbbccmm.cc = (bin_dmr_id[2] & 0x3e) >> 1
  ssbbccmm.mm = (bin_dmr_id[2] & 0x01) * 256 + bin_dmr_id[3]
  ssbbccmm.ok = true

  return ssbbccmm
}

export function num2Hex(n) {
  return n.toString(16).padStart(2, '0').toUpperCase()
}

export function ssbbccmm2dmrid(ss, bb, cc, mm, is_group, pp) {
  var bin_dmr_id_0 = 0
  if (is_group !== 0) {
    bin_dmr_id_0 = 0x80
  }
  if (typeof pp !== 'undefined') {
    bin_dmr_id_0 |= pp
  }
  var bin_dmr_id_1 = ((ss << 3) | (bb >> 2)) & 0xff
  var bin_dmr_id_2 = (((bb & 0x03) << 6) | (cc << 1) | (mm >> 8)) & 0xff
  var bin_dmr_id_3 = mm & 0xff

  return num2Hex(bin_dmr_id_0) + num2Hex(bin_dmr_id_1) + num2Hex(bin_dmr_id_2) + num2Hex(bin_dmr_id_3)
}

export function ssmmm2dmrid(ss, mmm, is_group, pp) {
  const bb = (mmm & 0x7c000) >> 14
  const cc = (mmm & 0x3e00) >> 9
  const mm = mmm & 0x1ff

  return ssbbccmm2dmrid(ss, bb, cc, mm, is_group, pp)
}

// dmrId协议系统，dmrId最大为0xFFFCDF，除去高位5位系统id，余下为dmrId编号(0x07FCDF)
// dmrId>=0x07A120(520000)以上为任务组和临时组使用，仅限组呼
export function getDmrMaxNo(isGroup = false) {
  return isGroup ? 0x07a11f : 0x07ffff
}

export function getCommonOrgType() {
  // 常规单位的类型 0：未定义 1：虚拟单位 2：真实单位
  return [1, 2]
}

export function getDynamicGroupOrgType() {
  // 动态组单位类型 0:临时组->100 1：任务组->101
  return [100, 101, 102]
}

// 设备类型 0:对讲机手台 1：车台 2:指挥座席 3:电话网关设备 4:中继虚拟终端 5:互联网关终端
// 6:模拟网关终端 7:数字网关终端 8:2.4G物联巡查终端 9:传统常规dmr手台
// 14:prochat公网终端 15:prochat网关终端
// 10:sip网关终端 11:虚拟集群对讲手台 12:mesh网关终端 13:mesh终端 16:sip 电话终端 21:基地台  22:android模拟终端
// 23: poc终端
export const DeviceTypes = {
  Device: 0,
  Mobile: 1,
  CmdAgent: 2,
  PhoneRepeater: 3,
  VirtualRepeater: 4,
  InternetGateway: 5,
  AnalogGateway: 6,
  DigitalGateway: 7,
  SipGatewayDevice: 10,
  VirtualClusterDevice: 11,
  MeshGateway: 12,
  MeshDevice: 13,
  ProchatDevice: 14,
  ProchatGatewayDevice: 15,
  SipProtocolDevice: 16,
  FixedMobile: 21,
  MobileDevice: 22,
  UserCard: 8,
  GeneralDmr: 9,
  PocDevice: 23,
}

// 固定位置的终端类型，需要在地图上显示标记，暂时只处理指挥坐席
export const fixedDevices = [DeviceTypes.CmdAgent, DeviceTypes.PhoneRepeater, DeviceTypes.AnalogGateway, DeviceTypes.DigitalGateway, DeviceTypes.FixedMobile]

// 不支持信道配置的终端类型
export const notSupportedChannelTypes = [
  DeviceTypes.CmdAgent,
  DeviceTypes.PhoneRepeater,
  DeviceTypes.UserCard,
  DeviceTypes.GeneralDmr,
  DeviceTypes.MeshGateway,
  DeviceTypes.MeshDevice,
  DeviceTypes.SipGatewayDevice,
  DeviceTypes.PocDevice,
  DeviceTypes.MobileDevice,
]

// 支持电话黑白名称的终端类型，电话网关，SIP网关
export const supportedBlackWhiteListTypes = [DeviceTypes.PhoneRepeater, DeviceTypes.SipGatewayDevice]

// 不能编辑的终端类型，主要由控制器创建
export const CannotEditDeviceTypes = [DeviceTypes.VirtualRepeater, DeviceTypes.SipGatewayDevice, DeviceTypes.MeshGateway, DeviceTypes.ProchatGatewayDevice]
// 终端有部分类型是不参与命令调度的，需要在命令窗口中过滤
export const NotSupportCommandDeviceTypes = [
  DeviceTypes.PhoneRepeater,
  DeviceTypes.InternetGateway,
  DeviceTypes.AnalogGateway,
  DeviceTypes.DigitalGateway,
  DeviceTypes.UserCard,
  DeviceTypes.SipGatewayDevice,
  DeviceTypes.MeshGateway,
  DeviceTypes.GeneralDmr,
]
// 部分终端只支持部分命令
export const SupportPartCommandDeviceTypes = {
  // 只支持短信
  [DeviceTypes.MeshDevice]: ['cb31'],
  [DeviceTypes.CmdAgent]: ['cb31'],
}
export const SupportPartCommandDeviceTypesKeys = Object.keys(SupportPartCommandDeviceTypes)

/**
 * 检查终端是否支持指定命令
 * @param { string } dmrId 16进制字符串
 * @param { string } cmdLabel 需要判断的命令字符串
 * @returns { boolean } 支持命令返回true，否则返回false
 */
export function checkTerminalIsSupportCommand(dmrId, cmdLabel) {
  const device = bfglob.gdevices.getDataByIndex(dmrId)
  if (!device) return false

  // 在支持部分命令的配置表中，检查命令号是否匹配
  if (SupportPartCommandDeviceTypesKeys.includes(device.deviceType + '')) {
    const supportCommands = SupportPartCommandDeviceTypes[device.deviceType]
    return supportCommands.includes(cmdLabel)
  }

  // 默认都支持
  return true
}

/**
 * 过滤掉不支持的命令的目标参数，当前只需要过滤终端目标
 * @param {{ groud: string[], device: string[] }} target 需要过滤的目标，16进制DMRID
 * @param { string } cmdLabel 字符串命令号，如cb01, cb02...
 * @returns {{ groud: string[], device: string[] }} 过滤后的目标结果
 */
export function filterNotSupportCommandTarget(target, cmdLabel) {
  const result = {
    groud: [...target.groud],
    device: [],
  }
  target.device.forEach(dmrId => {
    if (checkTerminalIsSupportCommand(dmrId, cmdLabel)) {
      result.device.push(dmrId)
    }
  })

  if (target.device.length !== result.device.length) {
    const discardList = target.device.filter(item => !result.device.includes(item))
    bfglob.console.error(`[filterNotSupportCommandTarget] device check (${cmdLabel}) result: filter=${discardList.join(',')}`)
  }

  return result
}

// 如果是固定位置的终端，使用设置的经纬度覆盖最后的经纬度
export function assignFixedDeviceLonLat(device) {
  if (fixedDevices.includes(device.deviceType)) {
    let settings = {}
    try {
      settings = JSON.parse(device.setting)
    } catch (e) {
      bfglob.console.error('[assignFixedDeviceLonLat] parse device setting err', e)
    }
    device.lastLon = settings.lonLat?.lon ?? device.lastLon
    device.lastLat = settings.lonLat?.lat ?? device.lastLat
  }

  return device
}

export default {
  getDeviceMapLonLat,
  RootId,
  DefOrgRid,
  ImageRid,
  notEditDataPermission,
  notSendCmdPermission,
  notDeleteDataPermission,
  notRepeaterWfPermission,
  notIntercomWfPermission,
  notEditUserPermDataPermission,
  // 判断登录账户是否为默认权限单位下的账户
  isRootUser() {
    if (bfglob.userInfo.orgId === '00000000-0000-0000-0000-000000000000') {
      return true
    } else {
      return false
    }
  },
  downloadAndSaveFile(url, filename, mimetype) {
    var ajax = new XMLHttpRequest()
    ajax.open('GET', url, true)
    ajax.responseType = 'blob'
    ajax.onload = e => {
      this.saveAsFile(filename, e.target.response, mimetype)
    }
    setTimeout(function () {
      ajax.send()
    }, 0)
  },
  saveAsFile(filename, data, mimetype) {
    var defaultMime = 'application/octet-stream'
    var blob = new Blob([data], { type: mimetype || defaultMime })
    if (window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveBlob(blob, filename)
    } else {
      var elem = window.document.createElement('a')
      elem.href = window.URL.createObjectURL(blob)
      elem.download = filename
      document.body.appendChild(elem)
      elem.click()
      document.body.removeChild(elem)
    }
  },
  sleep(time) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve()
      }, time)
    })
  },
  getTableHead(head) {
    let __head = '<tr>'
    for (let i = 0; i < head.length; i++) {
      const item = head[i]
      __head += `<th class='${item.class || ''}'
                     style='width:${item.width || 'auto'};'
                >${item.title}</th>`
    }
    __head += '</tr>'
    return __head
  },
  getTableBody(head, body) {
    let __body = ''
    let __index = 0
    for (const k in body) {
      const item = body[k]
      __body += '<tr>'
      for (let i = 0; i < head.length; i++) {
        const __head = head[i]
        const __name = __head.data
        let __data = item[__name]
        if (__name === null) {
          // render:模拟dataTable列渲染方法,param:(data, type, row, meta)
          __data = __head.render ? __head.render(__data, 'cell', item, { row: __index }) : item.defaultContent || ''
        }

        __body += `<td  class='${__head.class || ''}'
                        style='width:${__head.width || 'auto'};'
                  >${__data}</td>`
      }
      __body += '</tr>'
      // 递增索引序号
      __index++
    }
    return __body
  },
  // ms_status hex to bin
  ms_status_hex2bin(ms_status) {
    if (!ms_status || ms_status.length < 12) {
      ms_status = '000000000000'
    }
    return this.hex2bin(ms_status)
  },
  toHex(obj) {
    if (typeof obj !== 'number') {
      bfglob.console.error('the param must be number:', obj)
      return obj
    }
    return parseInt(obj).toString(16).padStart(2, '0').toUpperCase()
  },
  uint8array2string(buf) {
    try {
      return String.fromCharCode.apply(null, buf)
    } catch (e) {
      bfglob.console.error('uint8array2string err:', e)
    }
  },
  hex2bin,
  bin2hex,
  hex2binWithSpace(hex) {
    var bytes = []
    hex = hex.replace(' ', '')
    for (var i = 0; i < hex.length - 1; i += 2) {
      bytes.push(parseInt(hex.substr(i, 2), 16))
    }
    return bytes
  },

  // buffer is an ArrayBuffer
  uint8Array2hex(buffer) {
    return Array.from(new Uint8Array(buffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
  },
  uint8Array2Array(uint8Array) {
    return Array.from(uint8Array)
  },
  array2Uint8Array(array) {
    const uint8Array = new Uint8Array(array.length)
    for (let i = 0; i < array.length; i++) {
      uint8Array[i] = array[i]
    }
    return uint8Array
  },
  uintToString(uintArray) {
    try {
      return this.uint8array2string(uintArray)
    } catch (e) {
      bfglob.console.error('uintToString err:', e)
    }
  },
  toHexDmrId,
  uint8ArrayAppendBuffer(buf1, buf2) {
    const tmp = new Uint8Array(buf1.length + buf2.length)
    tmp.set(buf1, 0)
    tmp.set(buf2, buf2.length)

    return tmp
  },
  uint8ArrayAppendBytes(target, bytes) {
    const len = bytes.length
    const tmp = new Uint8Array(len)
    for (let i = 0; i < len; i++) {
      tmp[i] = bytes[i]
    }
    return this.uint8ArrayAppendBuffer(target, tmp)
  },
  // DMRID 编码、解析
  int32Dmrid2Hex(int32Dmrid) {
    return int32Dmrid.toString(16).padStart(8, '0').toUpperCase()
  },
  dmrid2ssmmm,
  dmrid2ssbbccmm,
  ssmmm2dmrid,
  ssbbccmm2dmrid,
  encodeDMRID(bb, cc, mm, is_group) {
    if (bb === '' && cc === '' && mm === '') {
      return ''
    }
    if (bb === '') {
      bb = 0
    }
    if (cc === '') {
      cc = 0
    }
    if (mm === '') {
      mm = 0
    }
    return this.ssbbccmm2dmrid(parseInt(bfglob.sysId), parseInt(bb), parseInt(cc), parseInt(mm), is_group, parseInt(bfglob.salerId))
  },
  lngLatDiffLimit(lngLatDif, max = 99, min = 0) {
    if (lngLatDif > max) {
      return max
    } else if (lngLatDif < min) {
      return min
    } else {
      return lngLatDif
    }
  },
  calculateLatitudeRadiusDiff(radius) {
    return (radius / 1.85).toFixed(0)
  },
  calculateLongitudeRadiusDiff(lat, radius) {
    var lat_rad = lat * 0.017453292519444
    return (radius / 1.85 / Math.cos(lat_rad)).toFixed(0)
  },
  LonDiff2Radius(lon_diff) {
    var lon_diff_int = parseInt(lon_diff)
    return (lon_diff_int * 1.85).toFixed(0)
  },

  clearMem(vue_obj, isClear = true) {
    if (!vue_obj.isMini && isClear) {
      //清除数据
      vue_obj.dataTable.body = []
      vue_obj.tabsValue = 'query'
    }
  },
  // 关闭dialog回调
  closeDialogCallback(vue_obj) {
    if (bfglob.layout === 0) {
      return
    }
    vue_obj.dlgSize = 'small'
  },
  // 选择语言版本
  saveLang(lang) {
    bfStorage.saveLang(lang)
  },
  objToArray,
  cloneObj(obj) {
    if (typeof obj !== 'object') {
      return obj
    }
    return JSON.parse(JSON.stringify(obj))
  },

  handle_linePoint_for_lineMast(detail, pointData) {
    var lineItem = bfglob.glineMaster.get(detail.lineId)
    if (!lineItem) {
      return
    }
    var linePoint_data = null
    if (typeof pointData === 'undefined') {
      linePoint_data = bfglob.glinePoints.get(detail.pointId, true)
      if (!linePoint_data) {
        linePoint_data = {}
      }
    } else {
      linePoint_data = pointData
    }
    var pointObj = {
      rid: detail.pointId,
      aheadTime: detail.aheadTime,
      delayTime: detail.delayTime,
      pointNo: detail.pointNo,
      lineId: detail.lineId,
      pointRid: detail.pointId,
      lineDetailRid: detail.rid,
      pointName: linePoint_data.pointName || 'NA',
      pointId: linePoint_data.pointId || 'NA',
    }
    if (typeof lineItem.pointData === 'undefined') {
      lineItem.pointData = {}
    }
    var key = 'x' + pointObj.lineDetailRid
    lineItem.pointData[key] = pointObj
  },
  checkedDeviceLastLonValid(device) {
    if (device.lastLon <= 180 && device.lastLon >= -180 && device.lastLat <= 90 && device.lastLat >= -90) {
      return true
    }
    return false
  },
  // 检查对讲机最后定位数据是否有效
  checked_device_gps_is_valid(device) {
    var lastGpsInvalidTime = device.lastGpsInvalidTime
    var lastGpsTime = device.lastGpsTime
    if (lastGpsInvalidTime >= lastGpsTime) {
      return 2
    }
    // 如果是30分钟前的定位数据，则判断经纬度是否有效，有效则视为有效定位
    const time = bfglob.sysConfig.maxAgeOfGpsTime || 30
    var timer = new Date(bfTime.nowUtcTime()).getTime() - new Date(device.lastGpsTime).getTime() >= time * 60 * 1000
    const validLonLat = this.checkedDeviceLastLonValid(device)
    if (timer) {
      // 30分钟前
      if (validLonLat) {
        return 2
      }
    } else {
      if (validLonLat) {
        return 1
      }
    }

    return 0
  },
  getDateTypeName(type) {
    let dateTypeName = 'gdevices'
    switch (type) {
      case MapMarkerTypes.Device:
        dateTypeName = 'gdevices'
        break
      case MapMarkerTypes.LinePoint:
        dateTypeName = 'glinePoints'
        break
      case MapMarkerTypes.MapPoint:
        dateTypeName = 'gmapPoints'
        break
      case MapMarkerTypes.Controller:
        dateTypeName = 'gcontrollers'
        break
      case MapMarkerTypes.IotDevice:
        dateTypeName = 'giotDevices'
        break
    }
    return dateTypeName
  },
  getMarkerByKey(key, type) {
    const markerType = this.getDateTypeName(type)
    const dataManage = bfglob[markerType]
    if (dataManage) {
      return dataManage.getMarker(key)
    } else {
      return null
    }
  },
  getDataByKey(key, type) {
    const dataName = this.getDateTypeName(type)
    const dataManage = bfglob[dataName]
    if (dataManage) {
      return dataManage.get(key)
    } else {
      return null
    }
  },
  deviceIsInCalling(device) {
    if (typeof device.call_uuid === 'undefined') {
      return false
    }
    return device.call_uuid.length > 0
  },
  process_scheduling_group_address(groupCallNo) {
    var callAddrBin = 0x7f9df - groupCallNo + bfglob.sysId * 0x80000
    callAddrBin = (callAddrBin | 0x80000000) >>> 0
    var callAddrHex = callAddrBin.toString(16).toUpperCase()
    var data = {}
    switch (groupCallNo) {
      case 0:
      case 1:
        data = {
          groupCallNo: groupCallNo,
          title: 'allChannel',
          type: 1,
        }
        break
      case 2:
        data = {
          groupCallNo: groupCallNo,
          title: 'groupCall_1L',
          type: 1,
        }
        break
      case 3:
        data = {
          groupCallNo: groupCallNo,
          title: 'syncCall',
          type: 1,
        }
        break
      case 4:
        data = {
          groupCallNo: groupCallNo,
          title: 'groupCall_2L',
          type: 1,
        }
        break
      case 5:
        data = {
          groupCallNo: groupCallNo,
          title: 'groupCall_12L',
          type: 1,
        }
        break
    }
    bfglob.dmrAddr.set(callAddrHex, data)
  },
  process_scheduling_base_address(groupCallNo, baseNo) {
    var callAddrBin = bfglob.sysId * 0x80000 + 0x39df - groupCallNo + baseNo * 0x4000
    callAddrBin = (callAddrBin | 0x80000000) >>> 0
    var callAddrHex = callAddrBin.toString(16).toUpperCase()
    var data = {}
    switch (groupCallNo) {
      case 6:
        data = {
          groupCallNo: groupCallNo,
          baseNo: baseNo,
          title: 'BSGroupCall_2',
          type: 2,
        }
        break
      case 7:
        data = {
          groupCallNo: groupCallNo,
          baseNo: baseNo,
          title: 'BSGroupCall_3',
          type: 2,
        }
        break
      case 8:
        data = {
          groupCallNo: groupCallNo,
          baseNo: baseNo,
          title: 'BSGroupCall_23',
          type: 2,
        }
        break
      case 9:
        data = {
          groupCallNo: groupCallNo,
          baseNo: baseNo,
          title: 'BSGroupCall_3',
          type: 2,
        }
        break
      case 10:
        data = {
          groupCallNo: groupCallNo,
          baseNo: baseNo,
          title: 'allChannel',
          type: 2,
        }
        break
    }
    bfglob.dmrAddr.set(callAddrHex, data)
  },
  process_scheduling_channel_address(groupCallNo, baseNo, channel) {
    var callAddrBin = bfglob.sysId * 0x80000 + baseNo * 0x40000 + 0x1df - groupCallNo + channel * 0x200
    callAddrBin = (callAddrBin | 0x80000000) >>> 0
    var callAddrHex = callAddrBin.toString(16).toUpperCase()
    var data = {}
    switch (groupCallNo) {
      case 11:
        data = {
          groupCallNo: groupCallNo,
          baseNo: baseNo,
          channel: channel,
          title: 'allChannel',
          type: 3,
        }
        break
      case 12:
        data = {
          groupCallNo: groupCallNo,
          baseNo: baseNo,
          channel: channel,
          title: 'allChannel',
          type: 3,
        }
        break
    }
    bfglob.dmrAddr.set(callAddrHex, data)
  },
  calc_bc11_target_dmrId_address() {
    for (var i = 0; i < 6; i++) {
      // 组呼号
      this.process_scheduling_group_address(i)
    }
    for (var j = 6; j < 11; j++) {
      for (var k = 0; k < 32; k++) {
        // 组呼号、基站号
        this.process_scheduling_base_address(j, k)
      }
    }
    for (var x = 11; x < 13; x++) {
      for (var y = 0; y < 32; y++) {
        for (var z = 0; z < 29; z++) {
          // 组呼号、基站号、信道号
          this.process_scheduling_channel_address(x, y, z)
        }
      }
    }
  },
  // 解析用户输入的DMRID
  inputDmrId() {
    return new Promise((resolve, reject) => {
      ElMessageBox.prompt(i18n.global.t('msgbox.resolveDmrIdMsg'), i18n.global.t('dialog.alertTitle'), {
        confirmButtonText: i18n.global.t('dialog.confirm'),
        cancelButtonText: i18n.global.t('dialog.cancel'),
        inputValidator: function (val) {
          if (!val || '' + val.length !== 8) {
            return false
          }
        },
        inputErrorMessage: i18n.global.t('msgbox.resolveDmrIdErr'),
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(res => {
          resolve(res)
        })
        .catch(cancel => {
          reject(cancel)
        })
    })
  },
  ss2SysId(ss) {
    return ss.toString().padStart(2, '0')
  },
  globalResoleDmrId() {
    var that = this
    return new Promise((resolve, reject) => {
      that
        .inputDmrId()
        .then(res => {
          if (!res.value) {
            reject(0)
            return
          }
          var dmrId = res.value.toUpperCase()
          var bbccmm = that.dmrid2ssbbccmm(dmrId)
          if (this.ss2SysId(bbccmm.ss) !== bfglob.sysId) {
            bfNotify.messageBox(i18n.global.t('msgbox.sysIdError'), 'warning')
            reject(0)
          } else {
            bbccmm.dmrId = dmrId
            resolve(bbccmm)
          }
        })
        .catch(e => {
          bfglob.console.log('globalResoleDmrId catch error:', e)
          reject(0)
        })
    })
  },
  // DMRI 检测规则
  check_DMID_rule(rule, value, callback, min, max) {
    if (isNaN(value)) {
      callback(new Error(i18n.global.t('msgbox.mustNumber')))
    } else if (value < min || value > max) {
      callback(new Error(min + ' ~ ' + max))
    } else {
      callback()
    }
  },
  mustIpRuleOrDormain(rule, value, callback) {
    const ip_reg =
      /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const domain_reg = /^(?=^.{3,255}$)((https?):\/\/)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/
    if (ip_reg.test(value) || domain_reg.test(value)) {
      callback()
    } else if (!value) {
      callback(new Error(i18n.global.t('dialog.requiredRule')))
    } else {
      callback(new Error(i18n.global.t('msgbox.mustIpOrDomain')))
    }
  },
  mustIpRule(rule, value, callback) {
    const reg =
      /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    if (reg.test(value)) {
      callback()
    } else if (!value) {
      callback(new Error(i18n.global.t('dialog.requiredRule')))
    } else {
      callback(new Error(i18n.global.t('msgbox.mustIp')))
    }
  },
  mustIpPort(rule, value, callback) {
    if (isNaN(value)) {
      callback(new Error(i18n.global.t('msgbox.mustNumber')))
    } else if (value > 65535) {
      callback(new Error(i18n.global.t('msgbox.portLimit')))
    } else {
      callback()
    }
  },
  mustNumberRule(rule, value, callback) {
    // // 检测输入的字符串只能为数字或者空值
    // let reg = /^[0-9]+$/;
    // if (!value || reg.test(value)) {
    //   callback();
    // } else {
    //   callback(new Error(this.$t("msgbox.mustNumber")));
    // }
    if (isNaN(value)) {
      callback(new Error(i18n.global.t('msgbox.mustNumber')))
    } else {
      callback()
    }
  },
  cannotIncludeChineseRule(rule, value, callback) {
    // 检测输入的字符串是否有中文
    const reg = /^[^\u4E00-\u9FA5]+$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error(i18n.global.t('msgbox.cannotInputChinese')))
    }
  },
  // 重置Vue Form 表单数据
  resetForm(vm, refName) {
    vm && vm.$refs[refName] && vm.$refs[refName].resetFields()
  },
  // 删除数据后重绘 dataTable 表格
  deleteDataTableRowData(dataArray, rid) {
    for (var k = 0; k < dataArray.length; k++) {
      var item = dataArray[k]
      if (item.rid === rid) {
        dataArray.splice(k, 1)
        break
      }
    }
  },
  updateDataTableRowData(dataArray, newData) {
    for (var k = 0; k < dataArray.length; k++) {
      var item = dataArray[k]
      if (item.rid === newData.rid) {
        dataArray[k] = Object.assign(item, newData)
        break
      }
    }
  },
  numberAutoIncrement(mm, maxVulue) {
    if (!mm) {
      return 1
    }
    var num = parseInt(mm)
    num++
    if (num > maxVulue) {
      return maxVulue
    } else {
      return num
    }
  },
  dmrIdAutoIncrement(dmrIdHex) {
    return (parseInt(dmrIdHex, 16) + 1).toString(16).padStart(8, '0').toUpperCase()
  },
  customNumberIncrement(str, len = 3) {
    const createId = (val = '1') => {
      return ('' + val).padStart(len, '0')
    }
    if (!str) {
      return createId()
    }

    str += ''
    const matchArr = str.match(/[\d]+|[\D]+/g)
    if (!matchArr || matchArr.length === 0) {
      str += createId()
      return str
    }

    const lastVal = matchArr.pop()
    const numReg = /\d+/
    if (numReg.test(lastVal)) {
      // 数字，加1
      return matchArr.join('') + createId(parseInt(lastVal) + 1)
    } else {
      str += createId()
      return str
    }
  },
  getBaseDataOrgId() {
    var orgItem = bfglob.gorgData.get(bfglob.userInfo.orgId)
    if (!orgItem) {
      return ''
    }
    return bfglob.userInfo.orgId
  },
  getBaseSelfId() {
    const orgItem = bfglob.gorgData.get(bfglob.userInfo.orgId)
    let selfId = ''
    if (orgItem) {
      selfId = orgItem.orgShortName
    }
    return this.customNumberIncrement(selfId)
  },
  getBase64Image(img) {
    var canvas = document.createElement('canvas')
    canvas.width = img.width
    canvas.height = img.height

    var ctx = canvas.getContext('2d')
    ctx.drawImage(img, 0, 0, img.width, img.height)
    var ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase()
    var dataURL = canvas.toDataURL('image/' + ext)
    return dataURL
  },
  default_user() {
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAFH0lEQVR4nO2d25HiMBBFN4QJwSEQAiEQgkMghM2AEAiBCKgbAiEQgkOY/XBraGvMjGFttcDnVOlna7C07quWuvXwnz8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAr4SkD0lNKtHtgQWRtDmfz3/Vc5HUSfocKVdJJ0kH+/smuu3wJJI+zIiXO8aeWiSpjf7/wANI2lpv9obsTAwnSQdJe0mt/e3OSus8RS6ECx6hcmxcP4z04L2kjyee12ZC6iRtlmg7/Cdm/GPWY2cxlgnIi6CZ47kwI1nP1zM9/pfnN+75xzmfDf+JjeNfxpnb+K6e1tXTLFEHPIi5/jTTvyxlfFdXCiFPS9UDD2Cz99QrtwXqO5asD37BhWxFeqR5gRQZXEvUCXcwY6TeuCtYr59ztKXqhYzM/TeF607zjkPJesFhWbsQV+zCTpWuGww3IbuUrtuJr3jdYKjP64ckZlx2EAFE4SKA4uMwAqiAJIDz+fw3oG4EEA0CWDmVCKArXTcYwQLwOQhCwdL4lGyQAHxKmIxgSezln2p4+WQEA5C08e5XCy4BT2hLWC5itfgJWKTxrS1HBFAY3XbmhC/HOg/AEFAKJ4DwECxyIrpaXAgWKoBsLtJEtmVVaLgRpIlqByuCgbgYfB9Uf2OTUNx/BG72HTIM+PqjI5FVkg0DbeG6v04K0fsDyXphU6jOrW5nA0KTUKsn8wJFFmTM6J82B8H40Wi4Krfo1nAVPoQCE3HZuEUnhGIncJ2owNKwD/tKTzphAj49vMSEkLCvcvyEcG4vYCnf1PtZ9KmVpZJDWe/fzvlsmBEtcH5f7iAoSZ8XQMNbPP4rLMxcPxdCvAoabtbcPvkMf/MIF0O9ElnC5lMPrhhaz/ciahdqKsyJ9dqdvl8S+ameZsLvD/p+lewFEVSMM7y/FrYzo+diOKlfzduoT+58SNra5o7ryO8RQq3odh9wbmTJXRKp4UWPU8qXtzChHBFCRagPzXJXnXrs3dtBTQh5r570e4QQTHLV+n77d2dieOhaWDPo1spGE9O79rcnhFCIH9x8Z/8ekpc34eRCuCKEmbCeNjYjl568/XsJ7gihE2sGz6HxUKyz8bfaq9rvzBE+EcIDqE/j5oavprdP4SchiGzifczQ3asaPkd9jgGPMAUN4/S3+zSLvuchZvuYxcujFW2z1vfP2RS737hKbMInrWibdSb4da8yZq5xG92eUmj4+Zl1HizVcJft6iZGGi5ZhxxwDcXNkFfh+sdQofMM1SG3d2/N++yyoaCNbk8xFPihh9rQbXEr/L6jYjjXt/qNllrbp+g03Lq97jjYUPBtJ0XJQr8muj014HIh7+8RFfiZl1pxGcL3jwac2lcX+99jVZNiN/630W2pBQ1vOtlGt2cxNLxYkQmgocALr4qi4Rc3m+j21ITzjO8bCWQRQBvdnhqw3v+VC3jrzGjmAVI5vPW4N4JuW9zHNr220e1bFFN7vrc/lYuFift3EYUZeyNpZ9vYLyNGT4dSXnob3EOoXwjZ/yCGVCTpaC+vtZ7TRLc/Z8TQumNsb/ST/Z/WYfR72MvzL+4nQfhytZd4SiJxQtnpduonHQRN5WNCSb/b2fP26l32wbyUN/A9I+flYr+ffBJptZihkijSC58qjMjSmaFPZux0EhmDz0EShqTW9fgkEKn3ClN75BRjpnJ1hvXGTcMSRq4R9e48uf5NVkaHheg2AwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPzKP5GJuJDSXhYpAAAAAElFTkSuQmCC'
  },
  get_device_virOrgsName(virOrgsRidArr) {
    var new_arr = []
    for (var i = 0; i < virOrgsRidArr.length; i++) {
      if (virOrgsRidArr[i].length === 0) {
        continue
      }

      var virOrgsName = bfglob.gdevices.getOrgNameByKey(virOrgsRidArr[i])
      new_arr.push(virOrgsName)
    }
    return new_arr.join(',')
  },
  get_device_userName(data) {
    let userName = ''
    if (typeof data.lastRfidPerson === 'undefined' || data.lastRfidPerson === '00000000-0000-0000-0000-000000000000') {
      userName = bfglob.gdevices.getUserNameByKey(data.deviceUser)
    } else {
      userName = bfglob.gdevices.getUserNameByKey(data.lastRfidPerson)
    }
    return userName
  },
  get_device_userImageFile(data) {
    let userImageFile = ''
    if (typeof data.lastRfidPerson === 'undefined' || data.lastRfidPerson === '00000000-0000-0000-0000-000000000000') {
      userImageFile = bfglob.gdevices.getUserImageFileByKey(data.deviceUser)
    } else {
      userImageFile = bfglob.gdevices.getUserImageFileByKey(data.lastRfidPerson)
    }
    return userImageFile
  },
  // 获取巡查点类型的base64格式图片
  getPointTypeFileContent(type) {
    switch (type) {
      case 1:
        return 'data:image/svg+xml;base64,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'
      case 2:
        return 'data:image/svg+xml;base64,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'
      case 3:
        return 'data:image/svg+xml;base64,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'
      case 4:
        return 'data:image/svg+xml;base64,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'
      default:
        return ''
    }
  },
  // 获取巡查点类型的名称
  getPointTypeName(type) {
    switch (type) {
      case 1:
        return i18n.global.t('dialog.Nsource')
      case 2:
        return i18n.global.t('dialog.Hsource')
      case 3:
        return i18n.global.t('dialog.Gsource')
      case 4:
        return i18n.global.t('dialog.BaseStationPoint')
      default:
        return ''
    }
  },
  // 重绘 datatable 表格
  redrawDataTable(vm) {
    vm.$refs.dataTable && vm.$refs.dataTable.drawDataTable(vm.dataTable.body)
  },
  columnsAdjust(vm) {
    vm.$refs.dataTable && vm.$refs.dataTable.columnsAdjust()
  },
  sortByProps,
  // 创建消息通知
  onSiteNotify(device, click_callback) {
    var title = i18n.global.t('dialog.notificationTitle')
    var message = ''
    let contentTitle = ''
    if (typeof device === 'string') {
      title = i18n.global.t('dialog.areaSearch')
      contentTitle = i18n.global.t('msgbox.notResAreaCmd')
    } else {
      contentTitle = device.selfId + ' / '
      if (device.userName) {
        contentTitle += device.userName + ' / '
      }
      contentTitle += device.orgShortName
      // const icon = device.userImageFile || bfglob.systemSetting.clientLogo || this.default_user()
      message = `<div class='siteNotify-content'>
                   <div class='content-title'>${contentTitle}</div>
                   <div class='content-message'>
                    <span class='bf-iconfont bfdx-a-touxiang2'></span>
                    <span class='message'>${device.promptContent}</span>
                   </div>
                 </div>`
    }

    const notifyClass = typeof device === 'string' ? 'notify_bc03' : 'notify_' + device.rid

    // 用于存储通知实例的变量
    let notifyInstance = null

    bfNotify
      .notifyBox({
        title: title,
        dangerouslyUseHTMLString: true,
        message: message,
        duration: 5 * 1000,
        // offset: 46,
        onClick: click_callback,
        customClass: notifyClass + ' bf-dispatch-notify',
        type: '',
        closeIcon: h('span', {
          class: 'close_icon',
          onClick: e => {
            e.stopPropagation()
            if (notifyInstance && typeof notifyInstance.close === 'function') {
              notifyInstance.close()
            }
          },
          alt: i18n.global.t('dialog.close'),
        }),
      })
      .then(instance => {
        // Promise resolve后保存实例
        notifyInstance = instance
      })
  },
  onDestopNotify(device, click_callback) {
    var that = this
    var options = {
      body: 'NA',
      data: 'NA',
      tag: 'NA',
    }
    var title = ''
    var __clientLogo = bfglob.systemSetting.clientLogo
    var __defaultLogo = `/logo.${bfglob.siteConfig?.logoExt || 'jpg'}`
    if (typeof device === 'string') {
      title = i18n.global.t('dialog.areaSearch')
      options = {
        body: i18n.global.t('msgbox.notResAreaCmd'),
        tag: 'bc03',
        icon: __clientLogo || __defaultLogo,
      }
    } else {
      title = device.selfId + ' / '
      if (device.userName) {
        title += device.userName + ' / '
      }
      title += device.orgShortName
      options = {
        body: device.promptContent,
        data: {
          deviceRid: device.rid,
          userRid: device.lastRfidPerson,
        },
        tag: device.selfId,
        icon: device.userImageFile || __clientLogo || __defaultLogo,
      }
    }

    var notification = new Notification(title, options)
    notification.onshow = function () {
      setTimeout(notification.close.bind(notification), 10 * 1000)
    }
    notification.onerror = function () {
      bfglob.console.error('notification error', notification)
      that.onSiteNotify(device, click_callback)
    }
    if (typeof click_callback === 'function') {
      notification.onclick = click_callback
    }
  },
  createNotify(device, click_callback) {
    // 检测浏览器是否有权限显示桌面通知，没有则申请
    if (!window.Notification || screenfull.isFullscreen) {
      bfglob.console.error('浏览器不支持 notification 或全屏中被屏蔽')
      this.onSiteNotify(device, click_callback)
    }
    if (window.Notification && Notification.permission !== 'granted') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          this.onDestopNotify(device, click_callback)
        } else {
          this.onSiteNotify(device, click_callback)
        }
      })
    } else {
      // 拥有权限
      this.onDestopNotify(device, click_callback)
    }
  },
  get_schedule_model(ctp_code) {
    var _ctp_code = ctp_code.toUpperCase()
    var _result = ''
    switch (_ctp_code) {
      case 'F':
        _result = i18n.global.t('msgbox.scheduleModalF')
        break
      case 'E':
        _result = i18n.global.t('msgbox.scheduleModalE')
        break
      case 'D':
        _result = i18n.global.t('msgbox.scheduleModalD')
        break
      case 'C':
        _result = i18n.global.t('msgbox.scheduleModalC')
        break
      case 'B':
        _result = i18n.global.t('msgbox.scheduleModalB')
        break
      case 'A':
        _result = i18n.global.t('msgbox.scheduleModalA')
        break
      case '9':
        _result = i18n.global.t('msgbox.scheduleModal9')
        break
      case '8':
        _result = i18n.global.t('msgbox.scheduleModal8')
        break
      case '7':
        _result = i18n.global.t('msgbox.scheduleModal7')
        break
      case '6':
        _result = i18n.global.t('msgbox.scheduleModal6')
        break
      case '5':
        _result = i18n.global.t('msgbox.scheduleModal5')
        break
      case '4':
        _result = i18n.global.t('msgbox.scheduleModal4')
        break
      case '3':
        _result = i18n.global.t('msgbox.scheduleModal3')
        break
      case '2':
        _result = i18n.global.t('msgbox.scheduleModal2')
        break
      case '1':
        _result = i18n.global.t('msgbox.scheduleModal1')
        break
    }
    return _result
  },
  get_devices_by_user_rid(userRid) {
    var result = []
    var g_device = bfglob.gdevices.getAll()
    for (var i in g_device) {
      if (g_device[i].lastRfidPerson === userRid) {
        result.push(g_device[i])
      }
    }
    return result
  },
  systemReload(action /* , instance */) {
    action === 'confirm' && window.location.reload(true)
  },
  uint32DmrId2Hex(uint32DmrId) {
    let hexDmrId = ''
    if (typeof uint32DmrId === 'number') {
      hexDmrId = uint32DmrId.toString(16).toUpperCase().padStart(8, '0')
    }
    return hexDmrId
  },
  sortChannels(channels, opts = { no: 'asc' }) {
    return channels.sort((a, b) => {
      return this.sortByProps(a, b, opts)
    })
  },
  stringToUtf8ByteArray(str) {
    // TODO(user): Use implementations if/when available
    var out = []
    var p = 0
    for (var i = 0; i < str.length; i++) {
      var c = str.charCodeAt(i)
      if (c < 128) {
        out[p++] = c
      } else if (c < 2048) {
        out[p++] = (c >> 6) | 192
        out[p++] = (c & 63) | 128
      } else if ((c & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {
        // Surrogate Pair
        c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff)
        out[p++] = (c >> 18) | 240
        out[p++] = ((c >> 12) & 63) | 128
        out[p++] = ((c >> 6) & 63) | 128
        out[p++] = (c & 63) | 128
      } else {
        out[p++] = (c >> 12) | 224
        out[p++] = ((c >> 6) & 63) | 128
        out[p++] = (c & 63) | 128
      }
    }
    return out
  },
  // HZ and MHZ transfer,1GHz=1000MHz，1MHz=1000kHz，1kHz=1000Hz
  frequencyHz2Mhz,
  frequencyMhz2Hz,
  noop,
  // ip编码和解码
  encodeStringIp,
  decodeInt32Ip,
  getActiveDevices,
  getRfidDevices,
}

export const MapMarkerTypes = {
  Device: 1,
  LinePoint: 2,
  MapPoint: 3,
  Controller: 4,
  IotDevice: 5,
}

export const MapMarkerClasses = {
  Device: 'devMarker',
  LinePoint: 'linePoint',
  MapPoint: 'mapPoint',
  Controller: 'ctrlMarker',
  IotDevice: 'iotMarker',
}

// RID_CALLER (uint32)
// 高8位	"0：无类型, 1：组呼, 2：单呼, 3：全呼"
// 低24位	1~16776415
export function dmrId2RidCaller(dmrId) {
  let high = 2
  const low = dmrId & 0x00ffffff
  // 全呼
  if (dmrId === 0x80ffffff) {
    high = 3
  } else if (dmrId >>> 24 === 0x80) {
    // 组呼
    high = 1
  }

  return ((high << 24) + low) >>> 0
}

// 控制器类型 0:中继 1:同播中继 2:电话网关 3:同播控制器 4: 虚拟集群控制器 5: 虚拟集群中继 10:sip网关
// 12:mesh网关 14:prochat网关 16: sip server网关
export const ControllerTypes = {
  Repeater: 0,
  SimulcastRepeater: 1,
  Gateway: 2,
  SimulcastController: 3,
  VCController: 4,
  VCRepeater: 5,
  SipGateway: 10,
  MeshGateway: 12,
  ProchatGateway: 14,
  SipServerGateway: 16,
}

/**
 * 获取指定范围的随机数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 生成的随机数
 */
export function getRandomRange(min, max) {
  return Math.random() * (max - min) + min
}

/**
 * 偏移定位数据坐标
 * @param location {[number, number]} 初始坐标 [经度, 纬度]
 * @param distance {number}  distance from the origin point
 * @param bearing {number}  ranging from -180 to 180
 * @param units {string}  'kilometers'  miles, kilometers, degrees, or radians
 * @returns {[number, number]} 偏移后的坐标 [经度, 纬度]
 */
export function offsetLocation(location, distance, bearing, units = 'kilometers') {
  const d = destination(location, distance, bearing, units)
  return d.geometry.coordinates
}

/**
 * 随机偏移一个坐标，偏移范围在1-10米
 * @param location {[number, number]} 初始坐标 [经度, 纬度]
 * @returns {[number, number]} 偏移后的坐标 [经度, 纬度]
 */
export function randomOffsetLocation(location) {
  // 偏移距离范围：0.005-0.01KM，即5-10米
  const distance = getRandomRange(0.005, 0.01)
  // 偏移方向范围：-120到120度，0度为正北方向
  const bearing = getRandomRange(-120, 120)
  // 计算偏移后的经纬度
  return offsetLocation(location, distance, bearing)
}

/**
 * 检查经度、纬度是否有效
 * @param lonLat {[number, number]} 需要检查的经、纬度数组
 * @return {boolean} true为有效
 */
export function lonLatValidate(lonLat) {
  const [lon, lat] = lonLat
  if (lon < -180 || lon > 180) {
    return false
  }

  return !(lat < -90 || lat > 90)
}

/**
 * 格式化DMRID显示
 * @param { string } dmrId 16进制字符串
 * @returns { string } 返回特定格式的字符串，eq: "001001BC / 1049020"
 */
export function formatDmrIdLabel(dmrId) {
  return `${dmrId} / ${parseInt(dmrId, 16) & 0xffffff}`
}

/**
 * Returns a Promise that has a resolve/reject methods that
 * can be used to resolve and defer the Deferred.
 * @returns {Promise<any>}
 */
export function deferred() {
  let methods = {}
  const p = new Promise((resolve, reject) => {
    methods = { resolve, reject }
  })
  return Object.assign(p, methods)
}

export function checkDmrIdIsGroup(dmrId) {
  return parseInt(dmrId, 16) >>> 24 === 0x80
}

// 获取url query sysId
export function getSysIdFromUrl() {
  const url = window.location.href
  const queryString = url.split('?')[1] || ''
  const params = new URLSearchParams(queryString)
  return params.get('sysId') || '00'
}

// 获取当前系统号
export async function getSysId() {
  try {
    const res = await fetch('/active_sys_ids')
    if (!res.ok) throw new Error('Failed to fetch active system IDs')

    const activeIds = await res.json()
    const urlSysId = getSysIdFromUrl()
    const accountSystem = JSON.parse(bfStorage.getItem('bfdx_account') || '{}')?.system

    // 上一次使用系统号 > url query > 服务器获取的系统号
    const useSysId = [accountSystem, urlSysId, activeIds[0]].find(id => id && activeIds.includes(id)) || '00'

    bfglob.sysId = useSysId
    return useSysId
  } catch (err) {
    console.error('[getSysId] 获取系统 ID 失败:', err)
    bfglob.sysId = '00'
    return '00'
  }
}
