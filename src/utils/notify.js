import screenfull from 'screenfull'
import i18n from '@/modules/i18n'
import { h, createApp } from 'vue'
import bfutil from './bfutil'
import ElMessageDetailContent from '@/components/common/ElMessageDetailContent.vue'
import bfDialog from '@/components/bfDialog/main'
import bfButton from '@/components/bfButton/main'

const Notify = {}
export const Types = {
  success: 'success',
  info: 'info',
  warning: 'warning',
  error: 'error',
}

export const bfMessageType = {
  info: 'info',
  success: 'success',
  warning: 'warning',
  error: 'error',
}

const colorType = {
  success: '#1398E9',
  warning: '#FF801D',
  info: '#9A9A9A',
  error: '#FF4E4E',
}

const bfMessageTypeOptions = {
  info: {
    icon: 'bfdx-tongzhixiaoxi',
    color: colorType.info,
  },
  success: {
    icon: 'bfdx-chenggong',
    color: colorType.success,
    textColor: '#00385A',
  },
  warning: {
    icon: 'bfdx-tongzhixiaoxi',
    color: colorType.warning,
  },
  error: {
    icon: 'bfdx-cuowutishi',
    color: colorType.error,
  },
}

const Options = {
  type: Types.info,
  dangerouslyUseHTMLString: false,
  closeOnClickModal: false,
  closeOnPressEscape: false,
}
const MessageOptions = {
  ...Options,
  message: '',
  showClose: true,
  duration: 4500,

  // 自定义属性
  isVnode: false,
  detailMessage: '',
}
const NotifyOptions = {
  ...Options,
  title: '',
  message: '',
  type: Types.info,
  duration: 4500,
  offset: 0,
  position: 'bottom-right',
}
const PromptOptions = {
  ...Options,
}

export function warningBox(msg = '', _type = Types.warning, _className = '') {
  return new Promise((resolve, reject) => {
    try {
      // 创建一个临时的 DOM 容器
      const container = document.createElement('div')
      document.body.appendChild(container)

      // 清理函数
      const cleanup = () => {
        setTimeout(() => {
          app.unmount()
          if (container.parentNode) {
            container.parentNode.removeChild(container)
          }
        }, 100)
      }

      const handleConfirm = () => {
        cleanup()
        resolve('confirm')
      }

      const handleClose = () => {
        cleanup()
        resolve('close')
      }

      // 创建 Vue 应用
      const app = createApp({
        data() {
          return {
            visible: true,
          }
        },
        methods: {
          handleConfirm,
          handleClose,
          onUpdateModelValue(value) {
            if (!value) {
              this.handleClose()
            }
          },
        },
        render() {
          return h(
            bfDialog,
            {
              modelValue: this.visible,
              title: i18n.global.t('dialog.alertTitle'),
              width: '400px',
              closeOnClickModal: false,
              closeOnPressEscape: false,
              showClose: true,
              top: '30vh',
              center: true,
              'onUpdate:modelValue': this.onUpdateModelValue,
              onClose: this.handleClose,
            },
            {
              default: () =>
                h('div', { class: 'warning-box-content flex justify-center' }, [
                  h('span', {
                    class: 'bf-iconfont bfdx-tongzhixiaoxi',
                    style: { marginRight: '10px', color: '#FF801D' },
                  }),
                  h('span', msg),
                ]),
              footer: () =>
                h('div', { class: 'flex justify-center' }, [
                  h(
                    bfButton,
                    {
                      colorType: 'warning',
                      onClick: this.handleConfirm,
                    },
                    () => i18n.global.t('dialog.confirm')
                  ),
                ]),
            }
          )
        },
      })

      // 挂载应用
      app.mount(container)
    } catch (error) {
      reject(error)
    }
  })
}

export function warningBoxWithOption(msg, options = {}) {
  return ElMessageBox.alert(msg, {
    ...Options,
    title: i18n.global.t('dialog.alertTitle'),
    confirmButtonText: i18n.global.t('dialog.confirm'),
    confirmButtonClass: `confirm_style ${options.className}`,
    ...options,
  })
}

export function messageBox(msg = '', type = Types.info, opts = {}) {
  return new Promise(function (resolve, reject) {
    try {
      let messageInstance = null

      const baseOptions = { ...MessageOptions, ...opts }

      let originalMessageContent
      if (baseOptions.isVnode) {
        originalMessageContent = h(ElMessageDetailContent, {
          message: msg,
          detailMessage: baseOptions.detailMessage,
        })
      } else {
        originalMessageContent = msg
      }

      const messageVNode = h('div', { style: 'display: flex; align-items: center; justify-content: space-between; width: 100%;' }, [
        // 左侧：原始消息内容
        h(
          'span',
          {
            style: {
              color: bfMessageTypeOptions[type]?.textColor || bfMessageTypeOptions[type].color,
              marginRight: '10px',
            },
          },
          originalMessageContent
        ),

        // 右侧：自定义的关闭按钮
        h(
          'div',
          {
            style: 'cursor: pointer;position: absolute;top: 0;right: 0;',
            onClick: e => {
              e.stopPropagation()
              if (messageInstance) {
                messageInstance.close()
              }
            },
          },
          [
            h('span', {
              class: 'bf-iconfont bfdx-guanbi',
              style: 'font-size: 42px; color: #3A6FCF;position: absolute;right: -32px;top: -4px;',
            }),
            h('span', {
              class: 'bf-iconfont bfdx-guanbi1',
              style: 'font-size: 16px; color: #fff;position: absolute;right: -20px;top: 15px;',
            }),
          ]
        ),
      ])

      const finalOptions = {
        ...baseOptions,
        type,
        icon: h('span', { class: `bf-icon bf-icon-${type} bf-iconfont ${bfMessageTypeOptions[type].icon}` }),
        message: messageVNode,
        showClose: false,
        customClass: `bf-message bf-message-${type} has-custom-close-btn`,
      }
      messageInstance = ElMessage(finalOptions)
      resolve(messageInstance)
    } catch (err) {
      reject(err)
    }
  })
}

// export function messageBox(msg = '', type = Types.info, opts = {}) {
//   return new Promise(function (resolve, reject) {
//     try {
//       const options = {
//         ...MessageOptions,
//         ...opts,
//         message: msg,
//         type,
//         icon: h('span', { class: `bf-icon bf-icon-${type} iconfont ${bfMessageTypeOptions[type].icon}` }),
//         customClass: `bf-message bf-message-${type}`,
//       }
//       if (options.isVnode) {
//         options.message = h(ElMessageDetailContent, {
//           message: options.message,
//           detailMessage: options.detailMessage,
//         })
//       }

//       resolve(ElMessage(options))
//     } catch (err) {
//       reject(err)
//     }
//   })
// }

export function notifyBox(opts = {}) {
  return new Promise((resolve, reject) => {
    try {
      // 同一个customClass的通知，应只现出一次。
      // 将通知实例缓存，生成新的通知前先将旧的关闭
      const options = {
        ...NotifyOptions,
        customClass: `${Date.now()}`,
        ...opts,
      }
      const prevNotify = Notify[options.customClass]
      if (prevNotify) {
        prevNotify.close()
        delete Notify[options.customClass]
      }

      const newNotify = ElNotification(options)
      Notify[options.customClass] = newNotify

      // 封装关闭通知事件，清除缓存的实例
      const originClose = newNotify.close
      newNotify.close = () => {
        originClose()
        delete Notify[options.customClass]
      }

      resolve(newNotify)
    } catch (err) {
      reject(err)
    }
  })
}

export function promptBox(opts) {
  const options = {
    ...PromptOptions,
    title: i18n.global.t('dialog.alertTitle'),
    cancelButtonText: i18n.global.t('dialog.cancel'),
    confirmButtonText: i18n.global.t('dialog.confirm'),
    inputErrorMessage: i18n.global.t('msgbox.processMoreRecordsErrMsg'),
    ...opts,
  }
  return ElMessageBox.prompt(options.message, options)
}

export function reqMaximumPrompt() {
  const message = `${i18n.global.t('msgbox.upperLimit')}${bfglob.reqMaximum}${i18n.global.t('dialog.records')},
  ${i18n.global.t('msgbox.processMoreRecords')}${i18n.global.t('msgbox.nextTimeEffect')}`
  const inputValidator = function (val) {
    if (isNaN(val)) {
      return i18n.global.t('msgbox.mustNumber')
    }
    if (bfglob.reqMaximum >= val) {
      return i18n.global.t('msgbox.processDataUpperLimitVal')
    }
    return true
  }
  const inputValue = bfglob.reqMaximum + 5000

  promptBox({
    message,
    inputValue,
    inputValidator,
  })
    .then(({ value, action }) => {
      if (action === 'confirm') {
        return { value: parseInt(value) }
      }
    })
    .then(({ value }) => {
      // 将用户输入的值转成数字
      bfglob.reqMaximum = value
    })
}

export default {
  warningBox,
  messageBox,
  notifyBox,
  promptBox,
  reqMaximumPrompt,
}

function isObject(o) {
  return Object.prototype.toString.call(o) === '[object Object]'
}

function onSiteNotify(options) {
  const icon = options.icon || bfglob.systemSetting.clientLogo || bfutil.default_user()
  const message = `<div class='siteNotify-content'>
    <img src='${icon}' class='siteNotify-image'>
    <span class='siteNotify-text'>${options.body}</span>
  </div>`

  notifyBox({
    title: options.title,
    dangerouslyUseHTMLString: true,
    message: message,
    type: 'info',
    duration: 8 * 1000,
    // offset: 25,
    onClick: typeof options.onClick === 'function' ? options.onClick : () => {},
    customClass: options.tag,
  })
}

function onDesktopNotify(options) {
  const opts = {
    body: options.body,
    data: options.data,
    tag: options.tag,
    icon: options.icon || bfglob.systemSetting.clientLogo || `/logo.${bfglob.siteConfig?.logoExt || 'jpg'}`,
  }

  const notification = new Notification(options.title, opts)
  notification.onshow = function () {
    setTimeout(notification.close.bind(notification), 10 * 1000)
  }
  notification.onerror = function () {
    bfglob.console.error('notification error', notification)
    onSiteNotify(options)
  }
  if (typeof options.onClick === 'function') {
    notification.onclick = options.onClick
  }
}

export function createNotification(options) {
  if (!isObject(options)) {
    return
  }
  // 检测浏览器是否有权限显示桌面通知，没有则申请
  if (!window.Notification || screenfull.isFullscreen) {
    bfglob.console.error('浏览器不支持notification或全屏中被屏蔽')
    onSiteNotify(options)
    return
  }

  if (!window.location.hostname.includes('https')) {
    onSiteNotify(options)
    return
  }

  if (window.Notification && Notification.permission !== 'granted') {
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        onDesktopNotify(options)
      } else {
        onSiteNotify(options)
      }
    })
  } else {
    // 拥有权限
    onDesktopNotify(options)
  }
}
