const designWidth = 1920 // 设计基准1920 默认在1920*1080分辨率下设计

// src/utils/setRem.ts
const setRem = () => {
  const baseFontSize = 16 // 1rem = 16px
  const clientWidth = document.documentElement.clientWidth
  const html = document.documentElement
  const scale = clientWidth / designWidth
  html.style.fontSize = baseFontSize * scale + 'px'
}

window.addEventListener('resize', setRem)
window.addEventListener('DOMContentLoaded', setRem)

setRem()

/**
 * 将px转换为rem
 * precision保留小数点后5位
 */
export function convertPxToRem(size: number, precision: number = 5): number {
  const baseFontSizeStr = document.documentElement.style.getPropertyValue('font-size')
  const baseFontSize = parseFloat(baseFontSizeStr) || 16
  const multiplier = Math.pow(10, precision + 1),
    wholeNumber = Math.floor((size / baseFontSize) * multiplier)
  return (Math.round(wholeNumber / 10) * 10) / multiplier
}

/**
 * 将px数值转换为rem, 16px -> 1rem
 */
export function convertPxToRemWithUnit(size: number, precision: number = 5): string {
  return `${convertPxToRem(size, precision)}rem`
}

/**
 * 计算缩放后的尺寸，传入设计尺寸，返回缩放后的尺寸
 * @example calcScaleSize(100) // 返回 100px 在当前屏幕下的缩放尺寸
 */
export function calcScaleSize(designSize: number): number {
  const clientWidth = document.documentElement.clientWidth
  const scale = clientWidth / designWidth
  return designSize * scale
}
