/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import { StrPubSub } from 'ypubsub'

export function on(subject: string | string[], handler: Function) {
  StrPubSub.subscribe(subject, handler)
}

export function once(subject: string, handler: Function) {
  StrPubSub.subscribeOnce(subject, handler)
}

export function off(subject: string | string[], handler: Function) {
  StrPubSub.unsubscribe(subject, handler)
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function emit(subject: string, ...args: any[]) {
  StrPubSub.publish(subject, ...args)
}

export default {
  on,
  once,
  off,
  emit,
}
