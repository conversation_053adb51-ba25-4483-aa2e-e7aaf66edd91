import { reactive, computed, ref } from 'vue'
import { cloneDeep, debounce } from 'lodash'
import bfProcess from '@/utils/bfprocess'
import { getDynamicGroupOrgType } from '@/utils/bfutil'
import dbCmd from '@/modules/protocol/db.pb.cmd'
import qWebChannelObj from '@/utils/qWebChannelObj'
import bfNotify from '@/utils/notify'
import eventBus from '@/utils/eventBus'
import { ProxyServer } from '@/envConfig'
import { v1 as uuid } from 'uuid'

// 通话信息接口定义
export interface SpeakInfo {
  speaker: string
  target: string
  maxSpeakTime: number
  priority: number
  listenGroup: string[]
}

// 设备频道接口
interface DeviceChannel {
  no: number
  listenGroup?: string[]
}

// 设备接口
interface Device {
  channels?: DeviceChannel[]
  selfId?: string
  dmrId?: string
}

// 组织接口
interface _Organization {
  orgShortName?: string
  dmrId?: string
  rid?: string
  orgIsVirtual?: number
}

// 动态组成员接口
interface _DynamicGroupMember {
  isDeviceGroup: number
  groupRid?: string
  deviceRid?: string
}

// 通话状态枚举
export enum SpeakState {
  ENDED = 0, // 结束讲话
  SPEAKING = 1, // 自己在讲话
  LISTENING = 2, // 他人在讲话
}

// 动态组类型
const dynamicGroupTypes = getDynamicGroupOrgType()

// 创建响应式的通话信息状态
const speakInfo = reactive<SpeakInfo>({
  speaker: '',
  target: '',
  maxSpeakTime: 60,
  priority: 3,
  listenGroup: [],
})

// 其他相关状态
const speakState = reactive({
  current: SpeakState.ENDED,
  otherSpeaking: false,
  currentSpeaker: '',
  pendingTarget: '',
  tempListenGroup: '',
})

// 全局标志，防止重复设置事件监听器
let qWebChannelListenersSetup = false

// 计算属性
const cmdAgentSpeakDevice = computed(() => {
  return bfglob.gdevices.getDataByIndex(speakInfo.speaker)
})

const cmdAgentExLisGroup = computed(() => {
  const skDevice = cmdAgentSpeakDevice.value as Device
  let lsGroup: string[] = []
  if (skDevice && skDevice.channels) {
    lsGroup = cloneDeep(skDevice.channels.find((item: DeviceChannel) => item.no === 1)?.listenGroup || [])
  }
  return lsGroup
})

const listenGroupInfo = computed(() => {
  const lsGroup = Array.from(new Set([...cmdAgentExLisGroup.value, ...speakInfo.listenGroup]))

  return lsGroup.map(dmrId => {
    return (bfglob.gorgData.getDataByIndex(dmrId) || bfglob.noPermOrgData.getDataByIndex(dmrId))?.orgShortName ?? ''
  })
})

const dynamicGroup = computed(() => {
  const dynamicGroup = bfglob.gorgData.getDataByIndex(speakInfo.target)
  return dynamicGroupTypes.includes(dynamicGroup?.orgIsVirtual) ? dynamicGroup : undefined
})

const dynamicGroupMemberInfo = computed(() => {
  const members = bfglob.gdynamicGroupDetail.getDataByGroupRid(dynamicGroup.value?.rid)
  return members.map((item: _DynamicGroupMember) => {
    // 根据数据类型，查找成员名称
    if (item.isDeviceGroup === 2) {
      return bfglob.gorgData.getDataMaybeNoPerm(item.groupRid)?.orgShortName ?? ''
    }
    return bfglob.gdevices.getDataMaybeNoPerm(item.deviceRid)?.selfId ?? ''
  })
})

const setupTarget = computed(() => {
  return speakState.pendingTarget || speakInfo.target
})

const speakDevice = computed(() => {
  const device = bfglob.gdevices.getDataByIndex(speakInfo.speaker)
  return device && device.selfId ? device.selfId : ''
})

const listenGroupName = computed(() => {
  const dmrId = speakInfo.listenGroup[0]
  const orgItem = bfglob.gorgData.getDataByIndex(dmrId)
  const len = speakInfo.listenGroup.length
  return orgItem ? `${orgItem.orgShortName} ${len > 1 ? '+' + (len - 1) : ''}` : ''
})

const targetName = computed(() => {
  const key = speakInfo.target

  // 判断是否为全呼目标
  if (key === bfglob.fullCallDmrId) {
    return '全呼' // 这里可以根据需要使用 i18n
  }

  const orgItem = bfglob.gorgData.getDataByIndex(speakInfo.target)
  if (orgItem) {
    return orgItem.orgShortName || orgItem.dmrId || ''
  } else {
    const device = bfglob.gdevices.getDataByIndex(key)
    if (device) {
      return device.selfId || device.dmrId || ''
    }
  }

  return ''
})

const callbackTargetName = computed(() => {
  const org = bfglob.gorgData.getDataByIndex(speakState.pendingTarget)
  if (org) {
    return org.orgShortName
  }
  const device = bfglob.gdevices.getDataByIndex(speakState.pendingTarget)
  if (device) {
    return device.selfId
  }

  return speakState.pendingTarget
})

// 初始化通话信息
function initSpeakInfo() {
  const info = bfglob.userInfo.setting.voipSpeakInfo
  let data

  // 如果没有设置通话目标，则以用户所在组为通话目标
  if (!info.target || !(bfglob.gorgData.getDataByIndex(info.target) || bfglob.gdevices.getDataByIndex(info.target))) {
    data = bfglob.gorgData.get(bfglob.userInfo.orgId)
    info.target = data?.dmrId || ''
  }
  // 如果没有设置接收组，则接收用户所在组的语音
  if (!info.listenGroup || info.listenGroup.length === 0) {
    data = data || bfglob.gorgData.get(bfglob.userInfo.orgId)
    info.listenGroup = data ? [data.dmrId] : []
  } else {
    // 需要过滤已经不存在的dmrId数据
    info.listenGroup = info.listenGroup.filter(item => !!bfglob.gorgData.getDataByIndex(item))
  }

  // 合并参数
  Object.assign(speakInfo, info)
}

// 设置通话目标
function setSpeakTarget(key: string) {
  // 先判断是否为全呼
  if (key === bfglob.fullCallDmrId) {
    speakInfo.target = key
    return
  }

  const orgItem = bfglob.gorgData.get(key)
  if (orgItem) {
    speakInfo.target = orgItem.dmrId || ''
  } else {
    const device = bfglob.gdevices.get(key)
    if (device) {
      speakInfo.target = device.dmrId || ''
    } else {
      speakInfo.target = ''
    }
  }
}

// 设置收听组
function setSpeakListenGroup(key: string) {
  const org = bfglob.gorgData.get(key)
  if (org && org.dmrId) {
    speakInfo.listenGroup.push(org.dmrId)
  }
}

// 比较收听组是否有变化
function compareListenGroupChanged(listenGroup: string[], oldListenGroup: string[]) {
  if (listenGroup.length !== oldListenGroup.length) {
    return true
  }
  for (let i = 0; i < listenGroup.length; i++) {
    const item = listenGroup[i]
    const hasItem = oldListenGroup.includes(item)
    if (!hasItem) {
      return true
    }
  }
  return false
}

// 更新用户通话信息设置
const updateUserVoipSpeakInfo = debounce(() => {
  const skInfo: Partial<SpeakInfo> = {}
  Object.assign(skInfo, speakInfo)
  skInfo.listenGroup = speakInfo.listenGroup.filter(item => !cmdAgentExLisGroup.value.includes(item))
  bfglob.userInfo.setting.voipSpeakInfo = skInfo
  bfglob.userInfo.setting.ispUdateVoipSpeakInfo = true
  bfProcess.updateUserSetting(JSON.stringify(bfglob.userInfo.setting), dbCmd.DB_USER_PUPDATE)
}, 500)

// 设置通话状态
function setSpeakState(state: SpeakState) {
  speakState.current = state
}

// 设置挂起目标
function setPendingTarget(speaker_dmr_id: string, speaker_target_dmr_id: string) {
  // 单呼自己，回呼目标需要设置为源目标
  if (speakInfo.speaker === speaker_target_dmr_id) {
    speakState.pendingTarget = speaker_dmr_id
  } else {
    speakState.pendingTarget = speaker_target_dmr_id
  }
}

// 清除挂起目标
function cleanPendingTarget() {
  speakState.pendingTarget = ''
}

// 恢复目标
function resumeTarget() {
  cleanPendingTarget()
  cleanTempListenGroup()
}

// 清除临时收听组
function cleanTempListenGroup() {
  speakState.tempListenGroup = ''
}

// 设置临时收听组
function setTempListenGroup() {
  // 跳过单呼目标
  const device = bfglob.gdevices.getDataByIndex(speakInfo.target)
  if (device) {
    return
  }
  // 组呼目标已存在收听组
  if (speakInfo.listenGroup.includes(speakInfo.target)) {
    return
  }

  speakState.tempListenGroup = speakInfo.target
}

// VoipServer 管理类
export class VoipServerManager {
  public voipServerConnected = ref(false)
  private pendingTime = 5000
  private pendingSsid = ''
  public hasUsbDevice = ref(false)
  public usbDeviceName = ref('')
  private localPlayingTimer = null
  private localRecordingTimer = null
  private checkSpeakingInterval = 2500
  public phoneNo = ref('')
  private transferTarget = ''
  private transferTargetTemp = ''
  private phoneTransferBc15 = { phoneDmrId: '', targetDmrId: '' }
  private otherSpeaking = false
  private currentSpeaker = ''
  private signalsConnected = false

  constructor() {
    // 不在构造函数中立即初始化，等待 qWebChannelObj 准备好
    if (!qWebChannelListenersSetup) {
      this.setupQWebChannelListeners()
      qWebChannelListenersSetup = true
    }
  }

  // 计算属性：获取 voipServer 实例
  get voipServer() {
    return qWebChannelObj && qWebChannelObj.server
  }

  // 获取连接状态
  get isConnected() {
    return this.voipServerConnected.value
  }

  // 获取USB设备状态
  get hasUsb() {
    return this.hasUsbDevice.value
  }

  get usbName() {
    return this.usbDeviceName.value
  }

  get phoneNumber() {
    return this.phoneNo.value
  }

  // 设置QWebChannel事件监听器
  private setupQWebChannelListeners() {
    // 监听QWebChannel服务器连接成功事件
    bfglob.on('QWebChannelServerConnected', () => {
      this.initVoipServer()
    })

    // 监听QWebChannel服务器退出事件
    bfglob.on('QWebChannelServerExit', () => {
      this.voipServerConnected.value = false
      this.setUsbStatus('')
      setSpeakState(SpeakState.ENDED)
      // 重置信号连接标志，以便重新连接时可以重新监听
      this.signalsConnected = false
    })
  }

  // 初始化 VoipServer
  initVoipServer() {
    if (this.voipServer) {
      this.initServerSuccess()
    } else {
      this.voipServerConnected.value = false
      speakState.pendingTarget = ''
    }
  }

  // 等待 qWebChannelObj 准备好后初始化
  async waitForQWebChannelAndInit(): Promise<boolean> {
    const maxRetries = 50 // 最多等待 5 秒
    let retries = 0

    return new Promise((resolve, reject) => {
      const checkAndInit = () => {
        if (this.voipServer) {
          this.initServerSuccess()
          resolve(true)
          return
        }

        retries++
        if (retries >= maxRetries) {
          console.warn('qWebChannelObj.server 未能在预期时间内初始化')
          this.voipServerConnected.value = false
          speakState.pendingTarget = ''

          // 等待超时后，监听 QWebChannelServerConnected 事件
          this.waitForQWebChannelServerConnected(resolve, reject)
          return
        }

        // 100ms 后重试
        setTimeout(checkAndInit, 100)
      }
      checkAndInit()
    })
  }

  // 监听 QWebChannelServerConnected 事件进行延迟初始化
  private waitForQWebChannelServerConnected(resolve: (value: boolean) => void, reject: (reason) => void) {
    // 监听 QWebChannelServerConnected 事件
    const onServerConnected = () => {
      bfglob.console.log('QWebChannelServerConnected - 延迟初始化 VoipServer')

      // 移除事件监听器
      bfglob.off('QWebChannelServerConnected', onServerConnected)

      // 检查 voipServer 是否可用
      if (this.voipServer) {
        // 调用完整的初始化流程
        this.initServerSuccess()
        resolve(true)
      } else {
        // 如果还是没有 voipServer，继续等待
        setTimeout(() => {
          if (this.voipServer) {
            this.initServerSuccess()
            resolve(true)
          } else {
            reject(new Error('voipServer 始终未能初始化'))
          }
        }, 1000)
      }
    }

    // 监听 QWebChannelServerConnected 事件
    bfglob.on('QWebChannelServerConnected', onServerConnected)
  }

  private initServerSuccess() {
    // 设置参数
    this.sl_setupVoipHost()
    this.sl_setupUserInfo()
    this.sl_setupTargetDmrid()
    this.sl_updateListenGroup()
    this.sl_setMaxSpeakingTime()

    // 监听 voipServer 信号
    this.listenVoipServerSingle()

    // 客户端连接服务器
    this.sl_connect_server()
    this.sl_getUsb3000Name()
  }

  private sl_setupVoipHost() {
    if (!this.voipServer) {
      return
    }

    const hostname = ProxyServer.hostname
    const port = ProxyServer.webPort
    this.voipServer.sl_setupVoipHost(hostname, port)
  }

  private sl_setupUserInfo() {
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_setupUserInfo(speakInfo.speaker, speakInfo.priority)
  }

  private sl_setupTargetDmrid() {
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_setupTargetDmrid(setupTarget.value)
  }

  sl_updateListenGroup(_listenGroup = speakInfo.listenGroup) {
    const exLsGroup = Array.from(new Set([...cmdAgentExLisGroup.value, ...speakInfo.listenGroup]))
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_updateListenGroup(exLsGroup.join(','))
  }

  private sl_setMaxSpeakingTime() {
    if (!this.voipServer || !speakInfo.maxSpeakTime) {
      return
    }
    this.voipServer.sl_setMaxSpeakingTime(speakInfo.maxSpeakTime)
  }

  private sl_connect_server() {
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_connect_server()
  }

  private setUsbStatus(name: string) {
    this.hasUsbDevice.value = !!name
    this.usbDeviceName.value = name
  }

  private sl_getUsb3000Name() {
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_getUsb3000Name((name: string) => {
      this.setUsbStatus(name)
    })
  }

  sl_i_speak_start(target = setupTarget.value) {
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_i_speak_start(speakInfo.speaker, target)
  }

  sl_i_speak_end() {
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_i_speak_end(speakInfo.speaker)
  }

  // 设置来电显示号码
  setPhoneNo(phoneNo = '') {
    // 设置号码时，如果是系统内的数据，则添加对应的名称
    if (phoneNo) {
      const phoneBook = bfglob.gphoneBook.getDataByIndex(phoneNo)
      if (phoneBook && phoneBook.phoneName) {
        phoneNo = `${phoneBook.phoneName}/${phoneNo}`
      }
    }
    this.phoneNo.value = phoneNo
  }

  // 检查通话会话是否应该过期
  checkCallSessionShouldExpired(timeout?: number) {
    const ssid = uuid()
    this.pendingSsid = ssid

    // 5秒挂起时间内，如果没有回呼对方，则结束挂起会话
    setTimeout(() => {
      // 讲话状态不为0,则有人在讲话，即回呼或继续呼叫
      if (speakState.current !== 0) {
        return
      }
      if (this.pendingSsid !== ssid) {
        return
      }

      // 会话id相同，则没有人回呼对方，结束挂起
      resumeTarget()
      this.setPhoneNo()
    }, timeout || this.pendingTime)
  }

  // 监听 VoipServer 信号
  private listenVoipServerSingle() {
    if (!this.voipServer || this.signalsConnected) {
      return
    }

    this.signalsConnected = true

    this.voipServer.sg_voip_register_ok.connect(() => {
      bfglob.console.log('sg_voip_register_ok')
      bfNotify.messageBox('USB服务器连接成功', 'success') // 这里可以根据需要使用 i18n
      this.voipServerConnected.value = true
      eventBus.emit('sg_voip_register_ok')
    })

    this.voipServer.sg_info.connect(_info => {})

    // 接收本机讲话结果消息
    this.voipServer.sg_want_to_speak_result.connect((msg: number) => {
      bfglob.console.log('sg_want_to_speak_result', msg, this.otherSpeaking)

      switch (msg) {
        case 0:
        case 2:
        case 1:
          setSpeakState(SpeakState.SPEAKING)
          setTempListenGroup()

          // 定时检测本地讲话
          clearInterval(this.localRecordingTimer)
          const sl_is_local_recording = () => {
            this.voipServer &&
              this.voipServer.sl_is_local_recording((localRecording: boolean) => {
                if (localRecording) {
                  // 本地讲话中，不处理
                  return
                }

                clearInterval(this.localRecordingTimer)

                // 没有其他人在讲话，则结束讲话状态
                if (speakState.current !== 2) {
                  setSpeakState(SpeakState.ENDED)
                }
                this.checkCallSessionShouldExpired(1)
              })
          }
          this.localRecordingTimer = setInterval(sl_is_local_recording, this.checkSpeakingInterval)
          break
        case 0x80:
          this.resumeOtherSpeakingStatus()
          bfNotify.messageBox('目标不在线')
          break
        case 0x81:
          this.resumeOtherSpeakingStatus()
          bfNotify.messageBox('目标通话中')
          break
        case 0x82:
          this.resumeOtherSpeakingStatus()
          bfNotify.messageBox('中继忙')
          break
        case 0x83:
          setSpeakState(SpeakState.LISTENING)
          bfNotify.messageBox('中继忙')
          break
        case 0x84:
          setSpeakState(SpeakState.LISTENING)
          bfNotify.messageBox('中继忙')
          break
        case 0x85:
          this.resumeOtherSpeakingStatus()
          bfNotify.messageBox('中继忙')
          break
        case 0x86:
          this.resumeOtherSpeakingStatus()
          bfNotify.messageBox('目标未登录')
          break
        case 0x94:
          this.resumeOtherSpeakingStatus()
          bfNotify.messageBox('动态组已失效')
          break
      }
    })

    // 添加其他监听器
    this.addOtherListeners()
  }

  private resumeOtherSpeakingStatus() {
    if (this.otherSpeaking) {
      setSpeakState(SpeakState.LISTENING)
    } else {
      setSpeakState(SpeakState.ENDED)
    }
  }

  // 继续添加其他监听器
  private addOtherListeners() {
    if (!this.voipServer) {
      return
    }

    // 监听其他人员讲话消息
    this.voipServer.sg_other_speaking.connect((speaker_dmr_id: string, speaker_target_dmr_id: string, start_end: number) => {
      bfglob.console.log('sg_other_speaking', speaker_dmr_id, speaker_target_dmr_id, start_end)

      if (start_end === 1) {
        // 其他人员讲话
        this.otherSpeaking = true
        setSpeakState(SpeakState.LISTENING)

        // 记录当前的讲话人员id
        this.currentSpeaker = speaker_dmr_id
        setPendingTarget(speaker_dmr_id, speaker_target_dmr_id)

        // 定时检测其他人员是否还在讲话
        clearInterval(this.localPlayingTimer)
        const sl_is_local_playing = () => {
          this.voipServer &&
            this.voipServer.sl_is_local_playing((localPlaying: boolean) => {
              if (localPlaying) {
                // 其他人员讲话，不处理
                return
              }

              clearInterval(this.localPlayingTimer)

              // 本地不处于讲话中，则结束讲话状态
              if (speakState.current !== 1) {
                setSpeakState(SpeakState.ENDED)
              }
              this.checkCallSessionShouldExpired(1)
            })
        }
        this.localPlayingTimer = setInterval(sl_is_local_playing, this.checkSpeakingInterval)
      } else {
        // 其他人员结束讲话
        this.otherSpeaking = false
        if (this.currentSpeaker !== speaker_dmr_id) {
          return
        }
        setSpeakState(SpeakState.ENDED)
        // 清除检测其他人员讲话定时器
        clearInterval(this.localPlayingTimer)

        this.checkCallSessionShouldExpired()
      }
    })

    // 接收在电话通话中取消收听组时，坐席无法正常接收指令时语音超时信号
    this.voipServer.sg_remote_speaking_timeout.connect(() => {
      this.checkCallSessionShouldExpired(1)
    })

    // 接收本地通话时间结束信息
    this.voipServer.sg_local_speaking_end.connect(() => {
      bfglob.console.log('sg_local_speaking_end')
      this.sg_local_speaking_end()
    })

    // 接收通话时间超时信息
    this.voipServer.sg_local_speaking_timeout.connect(() => {
      bfglob.console.log('sg_local_speaking_timeout')
      this.sg_local_speaking_end()
    })

    // 接收电话挂断信号
    this.voipServer.sg_endcall.connect((src_dmrid: string, target_dmrid: string, sound_type: number, phone_no: string) => {
      this.phoneEndCall(src_dmrid, target_dmrid, sound_type, phone_no)
    })

    // 接收缺失USB设备信息
    this.voipServer.sg_no_usb3000.connect(() => {
      this.setUsbStatus('')
    })

    this.voipServer.sg_usb3000_found.connect(() => {
      this.sl_getUsb3000Name()
    })
  }

  private sg_local_speaking_end() {
    setSpeakState(SpeakState.ENDED)
    // 清除检测本地讲话定时器
    clearInterval(this.localRecordingTimer)
    this.checkCallSessionShouldExpired()
  }

  private phoneEndCall(src_dmrid: string, target_dmrid: string, sound_type: number, phone_no: string) {
    bfglob.console.log('phoneEndCall', src_dmrid, target_dmrid, sound_type, phone_no)
    this.checkCallSessionShouldExpired(1)
    this.setPhoneNo()
  }

  // 电话相关方法
  telephoneHangup() {
    if (!this.voipServer) {
      return
    }
    this.voipServer.sl_end_call(this.phoneTransferBc15.phoneDmrId)
  }

  slPhoneTransfer() {
    if (!this.voipServer || !this.transferTarget) {
      return
    }
    this.voipServer.sl_phone_transfer(this.phoneTransferBc15.phoneDmrId, this.phoneTransferBc15.targetDmrId, this.transferTarget)
  }

  // 更新方法，当相关状态变化时调用
  updateUserInfo() {
    this.sl_setupUserInfo()
  }

  updateTargetDmrid() {
    this.sl_setupTargetDmrid()
  }

  updateMaxSpeakingTime() {
    this.sl_setMaxSpeakingTime()
  }

  // 监听 voipServer 变化
  watchVoipServer() {
    // 这个方法可以在外部调用来重新初始化
    this.initVoipServer()
  }

  // 获取电话号码相关方法
  getPhoneNumber() {
    return this.phoneNo.value
  }

  // 设置转接目标
  setTransferTarget(target: string) {
    this.transferTarget = target
  }

  // 设置电话转接信息
  setPhoneTransferBc15(info) {
    this.phoneTransferBc15 = info
  }

  // 清理资源
  destroy() {
    clearInterval(this.localPlayingTimer)
    clearInterval(this.localRecordingTimer)
  }
}

// 快速发起通话（封装自 bfSpeaking.vue）
async function speakFast(target: string): Promise<void> {
  // 无通话目标
  if (!target) return

  // 通话目标不应该为自己
  if (target === speakInfo.speaker) {
    bfNotify.messageBox('通话目标不能为自己', 'warning')
    return
  }

  // 等待 VoIP 注册成功
  await new Promise<void>(resolve => {
    if (globalVoipServerManager.isConnected) {
      return resolve()
    }

    // 未初始化TC918，订阅连接事件
    eventBus.once('sg_voip_register_ok', () => {
      resolve()
    })
  })

  // 在下一事件循环处理，以便通话目标可以正确设置
  setTimeout(() => {
    const voipServer = qWebChannelObj && qWebChannelObj.server
    const canSpeakBase = !!(voipServer && speakInfo.speaker && target && globalVoipServerManager.hasUsb && globalVoipServerManager.isConnected)

    if (!canSpeakBase) {
      return
    }

    // 自己正在讲话则先结束
    if (speakState.current === SpeakState.SPEAKING) {
      globalVoipServerManager.sl_i_speak_end()
    }

    // 发起讲话
    globalVoipServerManager.sl_i_speak_start(target)
  }, 0)
}

// 创建全局 VoipServerManager 实例
export const globalVoipServerManager = new VoipServerManager()

// 导出状态和方法
export {
  speakInfo,
  speakState,
  cmdAgentSpeakDevice,
  cmdAgentExLisGroup,
  listenGroupInfo,
  dynamicGroup,
  dynamicGroupMemberInfo,
  setupTarget,
  speakDevice,
  listenGroupName,
  targetName,
  callbackTargetName,
  initSpeakInfo,
  setSpeakTarget,
  setSpeakListenGroup,
  compareListenGroupChanged,
  updateUserVoipSpeakInfo,
  setSpeakState,
  setPendingTarget,
  cleanPendingTarget,
  resumeTarget,
  cleanTempListenGroup,
  setTempListenGroup,
  speakFast,
}
