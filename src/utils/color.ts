/**
 * 将16进制颜色转换为rgba颜色
 * @param color hex字符串，#fff or fff
 * @param alpha 透明度，0-100
 * @returns rgba字符串
 * @example hex2rgba('#fff', 50) // rgba(255, 255, 255, 0.5)
 */
export function hex2rgba(color: string, alpha: number = 100): string {
  // 移除开头的 '#'
  let cleanedHex = color.startsWith('#') ? color.slice(1) : color

  // 如果是简写形式（如FFF），扩展为完整形式（FFFFFF）
  if (cleanedHex.length === 3) {
    cleanedHex = cleanedHex
      .split('')
      .map(char => char + char)
      .join('')
  }

  // 确保是有效的十六进制颜色
  if (!/^[0-9A-Fa-f]{6}$/.test(cleanedHex)) {
    console.error('Invalid HEX color:', color)
    return ''
  }

  // 将十六进制转换为十进制
  const r = parseInt(cleanedHex.substring(0, 2), 16)
  const g = parseInt(cleanedHex.substring(2, 4), 16)
  const b = parseInt(cleanedHex.substring(4, 6), 16)

  // 返回 RGBA 格式的字符串
  return `rgba(${r}, ${g}, ${b}, ${alpha / 100})`
}
