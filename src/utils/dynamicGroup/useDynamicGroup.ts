import { onMounted, onBeforeUnmount, Ref } from 'vue'
import { addDynamicGroupNode, removeNode, updateOneOrgNodeTitle } from '@/utils/bftree'

/**
 * 动态组节点操作 Composables 函数
 * @param treeId - 动态组树的 ID 引用
 * @returns 动态组操作相关的方法和响应式数据
 */
export function useDynamicGroup(treeId: Ref<string>) {
  // 动态组及成员操作方法
  const handleAddDynamicGroupNode = source => {
    addDynamicGroupNode(treeId.value, source)
  }

  const handleUpdateDynamicGroupNode = source => {
    updateOneOrgNodeTitle(treeId.value, source)
  }

  const handleDeleteDynamicGroupNode = source => {
    removeNode(treeId.value, source.rid)
  }

  // 生命周期钩子
  onMounted(() => {
    // 订阅动态组数据请求完毕事件，加载树节点
    bfglob.on('add_one_dynamic_group', handleAddDynamicGroupNode)
    bfglob.on('update_one_dynamic_group', handleUpdateDynamicGroupNode)
    bfglob.on('delete_one_dynamic_group', handleDeleteDynamicGroupNode)
  })

  onBeforeUnmount(() => {
    // 清理事件监听器
    bfglob.off('add_one_dynamic_group', handleAddDynamicGroupNode)
    bfglob.off('update_one_dynamic_group', handleUpdateDynamicGroupNode)
    bfglob.off('delete_one_dynamic_group', handleDeleteDynamicGroupNode)
  })

  // 返回暴露给组件的方法和响应式数据
  return {
    addDynamicGroupNode: handleAddDynamicGroupNode,
    updateDynamicGroupNode: handleUpdateDynamicGroupNode,
    deleteDynamicGroupNode: handleDeleteDynamicGroupNode,
  }
}
