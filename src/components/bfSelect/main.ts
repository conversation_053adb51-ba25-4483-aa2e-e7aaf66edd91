import { defineComponent, h, computed, ref } from 'vue'
import { ElSelect } from 'element-plus'
import 'element-plus/es/components/select/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfSelect',
  props: {
    modelValue: {
      type: [String, Number, Boolean, Object, Array],
      default: '',
    },
    ...ElSelect.props,
  },
  emits: ['update:modelValue', 'change', 'visible-change', 'remove-tag', 'clear', 'blur', 'focus'],
  setup(props: InstanceType<typeof ElSelect>['$props'], { emit, slots, expose, attrs }) {
    const selectVal = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      },
    })

    const selectProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-select', attrs.class].filter(Boolean).join(' '),
        modelValue: selectVal.value,
        'onUpdate:modelValue': val => {
          selectVal.value = val
        },
        onChange: val => {
          emit('change', val)
        },
        onVisibleChange: visible => {
          emit('visible-change', visible)
        },
        onRemoveTag: tag => {
          emit('remove-tag', tag)
        },
        onClear: () => {
          emit('clear')
        },
        onBlur: event => {
          emit('blur', event)
        },
        onFocus: event => {
          emit('focus', event)
        },
      }
    })

    const suffixIcon = computed(() => {
      return h('span', {
        class: 'bf-iconfont bfdx-xiala text-[20px]',
      })
    })

    const selectRef = ref<InstanceType<typeof ElSelect>>()
    expose({
      selectRef,
      blur: () => selectRef.value?.blur(),
      focus: () => selectRef.value?.focus(),
    })

    // 使用 h 函数渲染 ElSelect
    return () =>
      h(
        ElSelect,
        {
          ...selectProps.value,
          popperClass: selectProps.value.popperClass + ' bf-select-popper',
          ref: selectRef,
          suffixIcon,
        },
        slots
      )
  },
})
