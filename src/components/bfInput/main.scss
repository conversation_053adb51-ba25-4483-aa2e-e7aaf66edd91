@use '@/css/common.scss';

.el-input.bf-input,
.el-textarea.bf-input {
  @extend .bf-component-size;

  --el-input-border-radius: 0;
  --el-input-bg-color: transparent;
  --el-disabled-bg-color: transparent;
  --el-input-border-color: rgba(148, 204, 232, 1);
  --el-input-focus-border-color: #fff;
  --el-input-text-color: #fff;
  --el-input-hover-border-color: rgba(148, 204, 232, 0.7);
  --el-input-icon-color: #fff;
  --el-input-clear-hover-color: rgba(255, 255, 255, 0.7);

  .el-input__wrapper,
  .el-textarea__inner {
    box-shadow: 0 0 0 var(--bf-border-size) var(--el-input-border-color, var(--el-border-color)) inset;

    &:hover {
      box-shadow: 0 0 0 var(--bf-border-size) var(--el-input-hover-border-color) inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 var(--bf-border-size) var(--el-input-focus-border-color) inset;
    }
  }

  &.el-input--large {
    font-size: 26px;
  }

  &.el-input--default {
    font-size: 16px;
  }

  &.el-input--small {
    font-size: 14px;
  }
}

.el-form-item.is-error .el-form-item__content .el-input.bf-input .el-input__wrapper {
  box-shadow: 0 0 0 var(--bf-border-size) var(--el-color-danger) inset;
}
