<template>
  <bf-dialog
    v-model="visible"
    :title="title + ' ' + $t('dialog.devAllStatus')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    class="deviceStateTable_dialog"
    center
  >
    <el-table :data="body" border class="bf-table deviceStateTable" :span-method="objectSpanMethod">
      <el-table-column :label="$t('dialog.workingSt')" :min-width="workMinWidth">
        <template #default="scope">
          <span class="device_st" :class="{ device_st_true: scope.row.workStatus }">√</span>
          <span v-text="scope.row.workLabel" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.settingSt')" :min-width="setMinWidth">
        <template #default="scope">
          <span class="device_st" :class="{ device_st_true: scope.row.settingStatus }">√</span>
          <span v-text="scope.row.settingLabel" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.alarmSt')" :min-width="minShortWidth">
        <template #default="scope">
          <span class="device_st" :class="{ device_st_true: scope.row.alarmStatus }">√</span>
          <span v-text="scope.row.alarmLabel" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.channelShiftLevel')" :min-width="channelMinWidth">
        <template #default="scope">
          <span class="device_st" :class="{ device_st_true: scope.row.channelStatus }">√</span>
          <span>
            <span v-text="scope.row.channelLabel || ''" />
            <span v-text="scope.row.channel" />
          </span>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <bf-button color-type="default" @click="visible = false">{{ $t('dialog.confirm') }}</bf-button>
      </span>
    </template>
  </bf-dialog>
</template>

<script>
  import { SupportedLang } from '@/modules/i18n'
  import bftree from '@/utils/bftree'

  import { utcToLocalTimeFormat } from '@/utils/time'
  import bfDialog from '@/components/bfDialog/main'
  import bfButton from '@/components/bfButton/main'

  export default {
    props: {
      title: {
        type: String,
        required: true,
        defalut: '00000000',
      },
      name: {
        type: String,
        required: true,
        defalut: '00000000',
      },
      device: {
        type: Object,
        required: true,
        defalut: {},
      },
    },
    data() {
      return {
        visible: true,
      }
    },
    methods: {
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        switch (columnIndex) {
          case 3:
            if (rowIndex < 5) {
              return {
                rowspan: 5,
                colspan: 1,
              }
            }
        }
      },
      getPrefixBit(index) {
        switch (index) {
          case 1:
            return 0x02
          case 2:
            return 0x04
          case 3:
            return 0x08
          case 4:
            return 0x10
          case 5:
            return 0x20
          case 6:
            return 0x40
          case 7:
            return 0x80
          default:
            return 0x01
        }
      },
      getLevelLabel(level) {
        switch (level) {
          case 1:
            return this.$t('dialog.low')
          case 2:
            return this.$t('dialog.mid')
          case 3:
            return this.$t('dialog.high')
          default:
            return this.$t('dialog.nothing')
        }
      },
    },
    mounted() {
      bfglob.emit('device_state@' + this.name)
    },
    computed: {
      body() {
        const statusList = [
          {
            workStatus: false,
            workLabel: this.$t('dialog.mobileTerminalType', {
              type: this.$t('dialog.mobile'),
            }),
            settingStatus: false,
            settingLabel: this.$t('dialog.enableEmergencyAlarm'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.emergencyAlarm'),
            channelStatus: false,
            channelLabel: this.$t('dialog.currentChannel'),
            channel: '',
          },
          {
            workStatus: false,
            workLabel: this.$t('dialog.hasBeenDisListen'),
            settingStatus: false,
            settingLabel: this.$t('dialog.openMobileCtrl'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.mobileCtrlAlarm'),
            channelStatus: false,
            channelLabel: false,
            channel: '',
          },
          {
            workStatus: false,
            workLabel: this.$t('dialog.hasBeenDisSend'),
            settingStatus: false,
            settingLabel: this.$t('dialog.openSentryCtrl'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.sentryCtrlAlarm'),
            channelStatus: false,
            channelLabel: false,
            channel: '',
          },
          {
            workStatus: false,
            workLabel: this.$t('dialog.hasGpsAutoCtrlLocate'),
            settingStatus: false,
            settingLabel: this.$t('dialog.openOutCtrl'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.outCtrlAlarm'),
            channelStatus: false,
            channelLabel: false,
            channel: '',
          },
          {
            workStatus: false,
            workLabel: this.$t('dialog.centralCtrlLocate'),
            settingStatus: false,
            settingLabel: this.$t('dialog.opentInCtrl'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.inCtrlAlarm'),
            channelStatus: false,
            channelLabel: false,
            channel: '',
          },
          {
            workStatus: false,
            workLabel: this.$t('dialog.hasEGAlarmAndLocate'),
            settingStatus: false,
            settingLabel: this.$t('dialog.openSchedulingFn'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.forciblyOutNetworkAlarm'),
            channelStatus: false,
            channelLabel: this.$t('dialog.level'),
            channel: '',
          },
          {
            workStatus: false,
            workLabel: this.$t('dialog.hasBeenRoaming'),
            settingStatus: false,
            settingLabel: this.$t('dialog.openListenFn'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.occurUnderVoltageAlarm'),
            channelStatus: false,
            channelLabel: this.$t('dialog.lastDataTime'),
            channel: '',
          },
          {
            workStatus: false,
            workLabel: this.$t('dialog.hasOffNetwork'),
            settingStatus: false,
            settingLabel: this.$t('dialog.openPeripheralFn'),
            alarmStatus: false,
            alarmLabel: this.$t('dialog.gpsAlarm'),
            channelStatus: false,
            channelLabel: false,
            channel: '',
          },
        ]
        const msStatusBin = this.device.msStatusBin

        for (let i = 0; i < statusList.length; i++) {
          const status = statusList[i]
          const bit = this.getPrefixBit(i)

          status.workStatus = !!(msStatusBin[0] & bit)
          status.scheduleStatus = !!(msStatusBin[1] & bit)
          status.settingStatus = !!(msStatusBin[2] & bit)
          status.alarmStatus = !!(msStatusBin[3] & bit)
        }
        let terminalType = this.$t('dialog.mobile')
        if (!statusList[0].workStatus) {
          // 对讲机
          terminalType = this.$t('dialog.interphone')
        }
        statusList[0].workLabel = this.$t('dialog.mobileTerminalType', {
          type: terminalType,
        })

        statusList[5].channelStatus = !!(msStatusBin[4] & 0x20)
        // 信道值
        statusList[0].channel = ' ' + bftree.getDeviceChannel(this.device)
        // 终端级别
        const levelVal = (msStatusBin[4] & 0xc0) >> 6
        statusList[5].channel = this.getLevelLabel(levelVal)
        // 最后数据时间
        statusList[7].channel = utcToLocalTimeFormat(this.device.lastDataTime)

        return statusList
      },
      isCN() {
        return this.$i18n.locale === SupportedLang.zhCN
      },
      isEN() {
        return this.$i18n.locale === SupportedLang.enUS
      },
      isFR() {
        return this.$i18n.locale === SupportedLang.fr
      },
      minShortWidth() {
        return this.isFR ? 260 : this.isEN ? 200 : 140
      },
      workMinWidth() {
        return this.isFR ? 240 : this.isEN ? 200 : 170
      },
      channelMinWidth() {
        return this.isFR ? 220 : this.isEN ? 200 : 160
      },
      setMinWidth() {
        return this.isFR ? 250 : this.isEN ? 210 : 130
      },
      fullscreen() {
        return !(this.$root.layoutLevel > 0)
      },
    },
    components: {
      bfDialog,
      bfButton,
    },
  }
</script>

<style lang="scss">
  .deviceStateTable_dialog {
    .el-dialog__body {
      padding: 0 10px;
      min-height: unset;

      .deviceStateTable.el-table {
        td,
        th {
          height: 22px;
          padding: 0;
        }

        td .cell {
          text-align: left;
          padding: 0;
        }

        .device_st {
          margin: 0 6px;
          width: 10px;
          height: 12px;
          color: transparent;
          font-weight: bold;
          visibility: hidden;
        }

        .device_st_true {
          color: #f00;
          visibility: visible;
        }
      }
    }

    .el-dialog__footer {
      display: flex;
      justify-content: center;
    }
  }
</style>
