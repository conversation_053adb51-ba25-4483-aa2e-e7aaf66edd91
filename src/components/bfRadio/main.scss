@use '@/assets/bfdxFont/iconfont.css';

.el-radio.bf-radio {

  --el-radio-input-height: 25px;
  --el-radio-input-width: 25px;
  --el-radio-unchecked-color: #1DDBFF;
  --el-radio-checked-color: #fff;

  margin-right: 10px;;
  .el-radio__input {

    .el-radio__inner {
      background-color: transparent;
      border: unset;
      &::before {
        @extend .bf-iconfont;
        @extend .bfdx-waikuang1;
        font-size: 25px;
        color: var(--el-radio-unchecked-color);
        width: 100%;
        height: 100%;
        display: block;
        line-height: 100%;
      }
      &::after {
        display: none;
      }
    }

    &.is-checked {
      .el-radio__inner {
        &::before {
          @extend .bfdx-xuanzhong;
          color: var(--el-radio-checked-color);
        }
      }
    }
  }

  .el-radio__label {
    font-size: 16px;
    color: var(--el-radio-unchecked-color);
  }

  &.is-checked {
    .el-radio__label {
      font-size: 16px;
      font-weight: bold;
      color: var(--el-radio-checked-color);
    }
  }
}