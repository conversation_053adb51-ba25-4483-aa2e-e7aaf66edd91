import { defineComponent, h, computed, ref } from 'vue'
import { ElRadio } from 'element-plus'
import 'element-plus/es/components/radio/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfRadio',
  props: {
    modelValue: {
      type: [String, Number, Boolean],
      default: undefined,
    },
    value: {
      type: [String, Number, Boolean],
      required: true,
    },
    ...ElRadio.props,
  },
  emits: ['update:modelValue'],
  setup(props: InstanceType<typeof ElRadio>['$props'], { emit, slots, expose, attrs }) {
    const radioValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })

    const finalProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-radio', attrs.class].filter(Boolean).join(' '),
        modelValue: radioValue.value,
        'onUpdate:modelValue': (val: string | number | boolean) => {
          radioValue.value = val
        },
      }
    })

    // 向父组件暴露组件实例
    const vmRef = ref<InstanceType<typeof ElRadio>>()
    expose({
      instance: vmRef,
    })

    // 使用 h 函数渲染
    return () =>
      h(
        ElRadio,
        {
          ...(finalProps.value as InstanceType<typeof ElRadio>['$props']),
          ref: vmRef,
        },
        slots
      )
  },
})
