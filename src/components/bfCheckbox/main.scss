@use '@/assets/bfdxFont/iconfont.css';

.el-checkbox.bf-checkbox {
  --el-checkbox-checked-text-color: #fff;
  --el-checkbox-text-color: #1ddbff;
  --el-checkbox-font-size: unset;
  --el-disabled-text-color: rgba(29, 219, 255, 0.5);

  font-size: inherit;

  .el-checkbox__input {
    background-color: unset;
    border-color: unset;

    .el-checkbox__inner {
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-color: unset;
      border: unset;
      width: 100%;
      height: 100%;

      @extend .bf-iconfont;
      @extend .bfdx-waikuang1;

      // 要覆盖.iconfont的font-size
      font-size: inherit;
      color: var(--el-checkbox-text-color);

      &::after {
        content: '';
        width: 0;
        height: 0;
        border: unset;
      }
    }

    &.is-checked {
      background-color: unset;
      border-color: unset;

      .el-checkbox__inner {
        --el-checkbox-checked-bg-color: transparent;
        --el-checkbox-checked-input-border-color: transparent;

        @extend .bfdx-xuanzhong;
        color: var(--el-checkbox-checked-text-color);
      }

      &.is-disabled {
        .el-checkbox__inner {
          --el-checkbox-disabled-checked-input-fill: transparent;
        }
      }
    }

    &.is-disabled {
      .el-checkbox__inner {
        color: var(--el-disabled-text-color);
      }
    }
  }

  .el-checkbox__label {
    font-size: inherit;
  }
}
