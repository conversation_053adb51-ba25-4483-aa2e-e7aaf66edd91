import { defineComponent, h, computed, ref } from 'vue'
import { ElCheckbox } from 'element-plus'
import 'element-plus/es/components/checkbox/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfCheckbox',
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    ...ElCheckbox.props,
  },
  emits: ['update:modelValue'],
  setup(props: InstanceType<typeof ElCheckbox>['$props'], { emit, slots, expose, attrs }) {
    const visible = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })

    // 构建传递给 ElCheckbox 的属性对象
    const finalProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-checkbox', attrs.class].filter(Boolean).join(' '),
        modelValue: visible.value,
        'onUpdate:modelValue': (val: boolean) => {
          visible.value = val
        },
      }
    })

    // 向父组件暴露组件实例
    const vmRef = ref<InstanceType<typeof ElCheckbox>>()
    expose({
      instance: vmRef,
    })

    // 使用 h 函数渲染
    return () =>
      h(
        ElCheckbox,
        {
          ...(finalProps.value as InstanceType<typeof ElCheckbox>['$props']),
          ref: vmRef,
        },
        slots
      )
  },
})
