.bf-btn-info {
  --bf-btn-text-color: rgb(var(--bf-btn-info-color));
  --bf-btn-stroke-color: var(--bf-btn-info-color);
  --bf-btn-fill-color: var(--bf-btn-info-color);
}

.bf-btn-warning {
  --bf-btn-text-color: rgb(var(--bf-btn-warning-color));
  --bf-btn-stroke-color: var(--bf-btn-warning-color);
  --bf-btn-fill-color: var(--bf-btn-warning-color);
}

.bf-btn-danger {
  --bf-btn-text-color: rgb(var(--bf-btn-danger-color));
  --bf-btn-stroke-color: var(--bf-btn-danger-color);
  --bf-btn-fill-color: var(--bf-btn-danger-color);
}

.bf-btn-primary {
  --bf-btn-text-color: rgb(var(--bf-btn-primary-color));
  --bf-btn-stroke-color: var(--bf-btn-primary-color);
  --bf-btn-fill-color: var(--bf-btn-primary-color);
}

.bf-btn-default {
  --bf-btn-text-color: rgb(var(--bf-btn-default-color));
  --bf-btn-stroke-color: var(--bf-btn-default-color);
  --bf-btn-fill-color: var(--bf-btn-default-color);
}

.bf-btn-wrapper {
  --el-font-size-base: var(--bf-base-font-size);
  position: relative;
  transition: transform 0.2s ease;
  width: var(--bf-btn-container-width, 120px);
  height: var(--bf-btn-container-height, 40px);

  // 暂定点击的状态
  &:not(.is-disabled):active {
    transform: scale(0.96);
  }

  &.is-disabled {
    filter: grayscale(0.5);
  }

  .bf-btn__renderer,
  .bf-btn {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
  }

  /* svg 背景 */
  .bf-btn__renderer {
    z-index: 1;
    transition: filter 0.3s ease;

    filter: drop-shadow(0 4px 8px rgba(var(--bf-btn-text-color, 255, 74, 74), 0.25));
  }

  .bf-btn.el-button {
    --el-button-bg-color: transparent;
    --el-button-hover-bg-color: transparent;
    --el-button-hover-border-color: transparent;
    --el-button-disabled-bg-color: transparent;
    --el-border: none;

    z-index: 2;

    padding: 0;
    color: var(--bf-btn-text-color);
    box-shadow: inset 0px -4px 6px rgba(var(--bf-btn-stroke-color), 0.16);
  }

  /* 边角 */
  .bf-btn__corner {
    position: absolute;
    width: calc(var(--bf-btn-corner-size) / 2);
    height: calc(var(--bf-btn-corner-size) / 2);
    z-index: 3;
    transition: filter 0.3s ease;

    background-color: var(--bf-btn-text-color);

    &.top-left {
      top: 0;
      left: 0;
      clip-path: polygon(0 0, 100% 0, 0 100%);
    }

    &.bottom-right {
      bottom: 0;
      right: 0;
      clip-path: polygon(100% 100%, 100% 0, 0 100%);
    }
  }

  &:hover {
    .bf-btn__renderer {
      filter: drop-shadow(0 6px 12px rgba(var(--bf-btn-text-color), 0.3)) brightness(1.1);
    }

    .bf-btn__corner {
      filter: brightness(1.2);
    }
  }
}
