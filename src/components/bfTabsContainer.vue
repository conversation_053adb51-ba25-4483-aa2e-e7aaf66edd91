<template>
  <div class="bf-tabs-container__wrapper" ref="tabsContainerRef">
    <!-- 1. SVG 背景层 -->
    <svg class="svg-background" width="100%" height="100%" preserveAspectRatio="none">
      <defs>
        <!--
            ID 和尺寸设置保持不变
            这个滤镜现在将按以下步骤工作：
            1. 创建外发光
            2. 创建内阴影
            3. 合并所有效果
          -->
        <filter id="custom-bg-filter2" x="-20%" y="-20%" width="140%" height="140%">
          <!-- ========================================================== -->
          <!--  1. 创建外发光 (替换 feDropShadow) -->
          <!-- ========================================================== -->

          <!-- a. 获取源图形的 Alpha 通道作为模糊的输入 -->
          <!--    'SourceAlpha' 是一个只包含形状轮廓的黑白图像 -->

          <!-- b. 对形状轮廓进行高斯模糊，'stdDeviation' 控制模糊半径 -->
          <feGaussianBlur in="SourceAlpha" stdDeviation="8" result="blur" />

          <!-- c. 创建一个指定颜色和透明度的颜色块 -->
          <!--    'flood-color' 是辉光的颜色，'flood-opacity' 是透明度 -->
          <feFlood flood-color="rgba(6, 121, 204, 1)" flood-opacity="0.46" result="color" />

          <!-- d. 将颜色块 'color' 填充到模糊后的形状 'blur' 中 -->
          <!--    'operator="in"' 表示只在 'blur' 的区域内显示 'color' -->
          <feComposite in="color" in2="blur" operator="in" result="outer-glow" />

          <!-- ========================================================== -->
          <!--  2. 创建内阴影 (这部分逻辑保持不变) -->
          <!-- ========================================================== -->
          <feComponentTransfer in="SourceAlpha" result="alpha-mask">
            <feFuncA type="table" tableValues="1 0" />
          </feComponentTransfer>
          <feGaussianBlur in="alpha-mask" stdDeviation="5" result="inner-blur" />
          <feFlood flood-color="#0ebeff" flood-opacity="0" result="inner-color" />
          <feComposite in="inner-color" in2="inner-blur" operator="in" result="inner-shadow" />

          <!-- ========================================================== -->
          <!--  3. 合并所有图层 -->
          <!-- ========================================================== -->
          <feMerge>
            <!-- 将我们刚刚创建的、带透明度的外发光放在最底层 -->
            <feMergeNode in="outer-glow" />

            <!-- 内阴影在中间层 -->
            <feMergeNode in="inner-shadow" />

            <!-- 原始图形（包含填充色和描边）放在最顶层 -->
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      <!-- 2. 动态绘制的路径 -->
      <path :d="svgPath" class="svg-path-shape" filter="url(#custom-bg-filter2)" vector-effect="non-scaling-stroke" />
    </svg>

    <!-- 3. Tabs 内容层，必须在 SVG 之上 -->
    <div class="bf-tabs-container" ref="tabsContentWrapperRef">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'

  interface Props {
    cutoutSize?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    cutoutSize: 16,
  })

  // --- Refs and state ---
  const tabsContainerRef = ref<HTMLElement | null>(null)
  /* eslint-disable @typescript-eslint/no-explicit-any */
  const tabsContentWrapperRef = ref<InstanceType<any> | null>(null)
  // --- Dimensions for SVG path ---
  const containerWidth = ref(0)
  const containerHeight = ref(0)
  const tabHeight = ref(40)
  const tabLeft = ref(0)
  const tabWidth = ref(0)

  // --- 4. 核心：动态计算 SVG 路径 ---
  const svgPath = computed(() => {
    const w = containerWidth.value
    const h = containerHeight.value
    const th = tabHeight.value
    const tl = tabLeft.value
    const tw = tabWidth.value

    // 安全检查，避免在尺寸为0时计算
    if (w === 0 || h === 0) return ''

    const cutoutSize = props.cutoutSize // 右下角斜切的尺寸

    // 使用 SVG path 命令 (M, L, Z)
    return `
    M 0,${th}
    L ${tl},${th}
    L ${tl},0
    L ${tl + tw},0
    L ${tl + tw},${th}
    L ${w},${th}
    L ${w},${h - cutoutSize}
    L ${w - cutoutSize},${h}
    L 0,${h}
    Z
  `
  })

  // --- Function to update dimensions ---
  const updateDimensions = async () => {
    await nextTick()
    const containerEl = tabsContainerRef.value
    const elTabsEl = tabsContentWrapperRef.value
    const tabsHeaderEl = elTabsEl?.querySelector('.el-tabs__header')
    const activeTabEl = elTabsEl?.querySelector('.el-tabs__item.is-active')

    if (!containerEl || !tabsHeaderEl || !activeTabEl) return

    const containerRect = containerEl.getBoundingClientRect()
    const activeTabRect = activeTabEl.getBoundingClientRect()

    containerWidth.value = containerRect.width
    containerHeight.value = containerRect.height
    tabHeight.value = tabsHeaderEl.offsetHeight
    tabLeft.value = activeTabRect.left - containerRect.left
    tabWidth.value = activeTabRect.width
  }

  // --- Lifecycle hooks ---
  onMounted(() => {
    updateDimensions()
    window.addEventListener('resize', updateDimensions)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('resize', updateDimensions)
  })

  // Expose for parent component interaction (e.g., in dialog)
  defineExpose({ updateDimensions })
</script>

<style scoped lang="scss">
  .bf-tabs-container__wrapper {
    position: relative;

    /* SVG 背景层 */
    .svg-background {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      pointer-events: none; /* 让鼠标事件穿透 SVG */

      /* SVG 路径的样式 */
      .svg-path-shape {
        /* 背景填充色 */
        fill: rgba(6, 121, 204, 0.46);
        /* 边框线颜色 */
        stroke: rgba(14, 190, 255, 0.74);
        stroke-width: 2;
        /* transition: d 0.35s cubic-bezier(0.4, 0, 0.2, 1); */
      }
    }

    /* Tabs 内容层 */
    .bf-tabs-container {
      position: relative;
      z-index: 2; /* 确保内容在 SVG 之上 */
      height: 100%;

      /* ... 其他 el-tabs 的样式覆盖 ... */
      :deep(.el-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;

        :deep(.el-tabs__content) {
          flex-grow: 1;
        }

        :deep(.el-tabs__active-bar),
        :deep(.el-tabs__nav-wrap::after) {
          display: none;
        }
      }
    }
  }
</style>
