<template>
  <div class="dialog-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :menuConfig="menuConfig"
      :withPageHeader="false"
      :filter="true"
      :checkAll="false"
      :checkStrictly="true"
      :needDestroyGlobalTree="true"
      :disabled-rids="disabledRids"
      @menu-click="menuEventHandler"
      :filter-source-data="filterSourceData"
      @checkbox-change="onCheckboxChange"
      @cell-dblclick="cellDbClickHandler"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, useTemplateRef, nextTick, watch, computed } from 'vue'
  import { MenuConfig, MenuEventHandler, TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import { VxeTableEvents } from 'vxe-table'
  import { checkDmrIdIsGroup } from '@/utils/bfutil'
  import { DeviceTypes } from '@/utils/bfutil'
  import { useI18n } from 'vue-i18n'

  const { t } = useI18n()

  // 支持查看设备状态的终端类型
  const SupportStatusDeviceTypes = [
    DeviceTypes.Device,
    DeviceTypes.Mobile,
    DeviceTypes.VirtualClusterDevice,
    DeviceTypes.PocDevice,
    DeviceTypes.VirtualRepeater,
  ]

  const statusContextMenuCode = ['stats', 'cb01', 'cb02', 'cb09']

  const emit = defineEmits(['checkbox-change', 'cell-dblclick'])

  const props = withDefaults(
    defineProps<{
      showOnlyOrg?: boolean
      defaultCheckKeys?: string[] | string
      disabledRids?: string[]
    }>(),
    {
      showOnlyOrg: false,
      defaultCheckKeys: () => [],
      disabledRids: () => [],
    }
  )

  const tableTreeRef = useTemplateRef('tableTree')

  const menuConfig = computed<MenuConfig>(() => {
    const config = {
      body: {
        options: [
          [
            {
              name: t('tree.collapseAll'),
              code: 'collapseAll',
            },
            {
              name: t('tree.expandAll'),
              code: 'expandAll',
            },
          ],
        ],
      },
      visibleMethod: ({ options, row }) => {
        if (!row) return true
        let hasStatus = false

        if (row.nodeType === TreeNodeType.Terminal) {
          const device = bfglob.gdevices.get(row.rid)
          // 只有指定的设备才显示状态
          if (device && SupportStatusDeviceTypes.includes(device.deviceType)) {
            hasStatus = true
          }
        }

        const visible = row.nodeType === TreeNodeType.Org ? false : hasStatus
        const disabled = !visible

        options?.forEach(list => {
          list.forEach(item => {
            if (statusContextMenuCode.includes(item.code)) {
              item.visible = visible
              item.disabled = disabled
            }
          })
        })

        return true
      },
    } satisfies MenuConfig
    if (!props.showOnlyOrg) {
      config.body.options.push([
        {
          name: t('tree.online'),
          code: 'displayOnline',
        },
        {
          name: t('tree.displayAllDev'),
          code: 'displayAll',
        },
      ])
    }
    return config
  })

  const menuEventHandler: MenuEventHandler = ({ menu, row }) => {
    switch (menu.code) {
      case 'collapseAll':
        tableTreeRef.value?.collapseAll()
        tableTreeRef.value
        break
      case 'expandAll':
        tableTreeRef.value?.expandAll()
        break
      case 'displayOnline':
        tableTreeRef.value?.displayOnline()
        break
      case 'displayAll':
        tableTreeRef.value?.displayAll()
        break
    }
  }

  const onCheckboxChange = (row, checked) => {
    emit('checkbox-change', row, checked)
  }
  const filterSourceData = (row: TreeNodeData) => {
    if (!props.showOnlyOrg) {
      return true
    }
    if (row.nodeType !== TreeNodeType.Org) {
      row.parentOrgId = ''
      return false
    }
    return true
  }

  const cellDbClickHandler: VxeTableEvents.CellDblclick<TreeNodeData> = ({ row }) => {
    // 只发送双击事件，传递行数据，让父组件处理具体逻辑
    emit('cell-dblclick', { row })
  }

  // 标准化 defaultCheckKeys 为数组格式
  const normalizeCheckKeys = (keys: string[] | string): string[] => {
    if (typeof keys === 'string') {
      return keys ? [keys] : []
    }
    return Array.isArray(keys) ? keys : []
  }

  // 设置节点勾选状态
  const setNodeCheckStatus = async (checkKeys: string[]) => {
    await nextTick()

    if (!tableTreeRef.value?.setCheckboxRowByRid) {
      return
    }

    // 首先清除所有节点的勾选状态
    if (tableTreeRef.value?.clearAllCheckboxRow) {
      tableTreeRef.value.clearAllCheckboxRow()
    }

    // 然后设置指定节点为勾选状态
    checkKeys.forEach(dmrId => {
      const isGroup = checkDmrIdIsGroup(dmrId)
      const data = isGroup ? bfglob.gorgData.getDataByIndex(dmrId) : bfglob.gdevices.getDataByIndex(dmrId)
      if (data && data.rid) {
        tableTreeRef.value.setCheckboxRowByRid(data.rid, true)
      }
    })
  }

  const setDefaultCheckedNodes = async () => {
    const checkKeys = normalizeCheckKeys(props.defaultCheckKeys)
    if (checkKeys.length === 0) {
      return
    }
    await setNodeCheckStatus(checkKeys)
  }

  // 监听 defaultCheckKeys 变化
  watch(
    () => props.defaultCheckKeys,
    newKeys => {
      const checkKeys = normalizeCheckKeys(newKeys)
      setNodeCheckStatus(checkKeys)
    },
    { deep: true }
  )

  onMounted(() => {
    setTimeout(() => {
      setDefaultCheckedNodes()
    }, 500) // 增加延迟时间确保树完全加载
  })

  defineExpose({
    tableTreeRef,
  })
</script>

<style lang="scss">
  .dialog-tree-wrapper {
    height: 100%;
    width: 100%;
    font-family: 'AlibabaPuHuiTi2';

    .vxe-table .vxe-table--render-wrapper .vxe-column-content {
      color: #1ac8ed;
    }
  }
</style>
