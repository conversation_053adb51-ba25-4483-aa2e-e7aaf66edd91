<template>
  <BfDialog
    v-model="listenGroupVisible"
    :title="$t('dialog.selectListenGroup')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    class="header-border footer-border select-listen-group-dialog"
    @close="onClose"
  >
    <!-- <TableTree
      :ref="treeId"
      :treeId="treeId"
      :option="wrapTreeOption"
      :filterOption="wrapFilterOption"
      @select="selectNodes"
      @click="clickNode"
      @loaded="onload"
    /> -->
    <dialog-table-tree class="w-full h-full m-auto" :showOnlyOrg="true" :defaultCheckKeys="modelValue" @checkbox-change="selectSendGroup"></dialog-table-tree>
  </BfDialog>
</template>

<script>
  import dialogTableTree from '@/components/common/dialogTableTree.vue'
  import { getOrgNodeTitle } from '@/utils/bftree'
  import BfDialog from '@/components/bfDialog/main'

  export default {
    emits: ['update:modelValue', 'update:visible'],
    props: {
      modelValue: {
        type: Array,
        required: true,
      },
      visible: {
        type: Boolean,
        required: true,
      },
      treeId: {
        type: String,
        required: true,
      },
      treeOption: {
        type: Object,
      },
      filterOption: {
        type: Object,
      },
      // 选中节点后，返回的节点原始数据的属性
      selectProp: {
        type: String,
        default: 'dmrId',
        validator: v => ['dmrId', 'rid'].includes(v),
      },
    },
    data() {
      return {}
    },
    methods: {
      selectSendGroup(row, checked) {
        const data = bfglob.gorgData.get(row.rid)
        if (!data || !data.dmrId) {
          return
        }
        if (!checked) {
          const keys = [...this.modelValue, data.dmrId]
          this.emitSelectNodes(keys)
        } else {
          const keys = this.modelValue.filter(d => d !== data.dmrId)
          this.emitSelectNodes(keys)
        }
      },
      onClose() {
        this.listenGroupVisible = false
      },
      onload() {
        this.tableTree
          ?.toDictTree('bftree', dict => {
            // 过滤非单位节点
            if (dict.data && (!dict.data.isOrg || !dict.folder)) {
              return false
            }
            // 过滤动态组
            if (dict.data?.orgIsVirtual >= 100) {
              return false
            }

            dict.selected = false
            // 设置默认选择状态
            if (this.selectProp === 'dmrId') {
              if (this.modelValue.includes(dict.data?.dmrId)) {
                dict.selected = true
              }
            } else if (this.selectProp === 'rid') {
              if (this.modelValue.includes(dict.key)) {
                dict.selected = true
              }
            }

            // 更新节点，重新生成节点title属性，主要去除计数标记
            const org = bfglob.gorgData.get(dict.key)
            if (org) {
              dict.title = getOrgNodeTitle(org, { showCounter: false })
            }

            return dict
          })
          .then(() => {
            this.tableTree.sortChildren()
            this.tableTree.updateViewport(this.tableTree.getLocalTree())
          })
      },
      emitSelectNodes(keys) {
        this.$emit('update:modelValue', keys)
      },
    },
    computed: {
      listenGroupVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      tableTree() {
        return this.$refs[this.treeId]
      },
      wrapTreeOption() {
        return {
          selectMode: 2,
          ...this.treeOption,
        }
      },
      wrapFilterOption() {
        return {
          leavesOnly: false,
          ...this.filterOption,
        }
      },
    },
    components: {
      BfDialog,
      dialogTableTree,
    },
  }
</script>

<style lang="scss">
  .bf-dialog.select-listen-group-dialog {
    width: 420px;
    height: 50%;

    .el-dialog__body {
      height: calc(100% - 45px);
    }
  }
</style>
