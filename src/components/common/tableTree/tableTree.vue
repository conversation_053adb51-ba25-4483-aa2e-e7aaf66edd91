<template>
  <section class="w-full h-full overflow-hidden fancytree-grid" :class="[{ 'has-filter': filter }, { 'with-page-header': withPageHeader }]">
    <template v-if="filter">
      <PageHeader v-if="withPageHeader">
        <template #title>
          <TreeFilterInput
            class="translate-x-[26px] translate-y-[-12px] you-she-biao-ti-hei"
            ref="filterInput"
            v-model="filterValue"
            @filter="filterOnChange"
            show-icon
            :is-in-page-header="withPageHeader"
          />
        </template>
      </PageHeader>
      <TreeFilterInput class="h-[35px]" v-else ref="filterInput" v-model="filterValue" @filter="filterOnChange" />
    </template>

    <div class="relative w-full fancytree-grid-wrapper">
      <div
        v-if="withPageHeader"
        class="relative flex justify-center items-center gap-[10px] h-[36px] w-[171px] bg-linear-to-r from-0% from-transparent via-20% via-[rgba(0,113,255,0.2)] to-100% to-transparent"
      >
        <span class="relative h-[24px] text-[18px]">BelFone</span>
        <span class="absolute right-0 h-[15px] px-[5px] bg-[#1a7aff] rounded-[4px] text-[11px]">{{ nodeNumbers ?? 11000 }}</span>
      </div>
      <div ref="fancytreeGridContainer" class="relative w-full fancytree-grid-container">
        <table :id="treeId" ref="tableTree" class="w-full bf-tree">
          <!-- <caption>Loading&hellip;</caption> -->
          <colgroup>
            <col width="*" />
          </colgroup>
          <thead>
            <tr>
              <th />
            </tr>
          </thead>
        </table>

        <div ref="scrollBar" class="fancytree-ext-table-scroll-bar" />

        <treeToolbar v-if="toolbar" class="treeOptBtns" />
      </div>
    </div>
  </section>
</template>

<script>
  import 'jquery'
  import 'jquery-ui/dist/jquery-ui'
  import 'ui-contextmenu'
  import 'jquery.fancytree'
  import 'jquery.fancytree/dist/modules/jquery.fancytree.filter'
  import 'jquery.fancytree/dist/modules/jquery.fancytree.grid'
  import 'jquery.fancytree/dist/skin-win8/ui.fancytree.css'
  import { debounce } from 'lodash'
  import bftree, { defaultTreeId, LocalTree, sortChildren, toDictTree } from '@/utils/bftree'
  import './plugins/plain-scrollbar.css'
  import PlainScrollbar from './plugins/plain-scrollbar.js'
  import { defineAsyncComponent } from 'vue'
  import { calcScaleSize } from '@/utils/setRem'

  const ContextmenuOption = {
    delegate: 'span.fancytree-node',
    autoFocus: true,
    menu: [],
    select: (_event, _ui) => {
      return false
    },
  }

  let resizeObserver

  export default {
    name: 'TableTree',
    emits: ['filterValueChange', 'loaded', 'select', 'dblclick', 'click', 'isFilterOnlineChange'],
    props: {
      toolbar: {
        type: Boolean,
        default: false,
      },
      treeId: {
        type: String,
        default: 'ft_' + Date.now(),
      },
      option: {
        type: Object,
      },
      selected: {
        type: Array,
        default: () => {
          return []
        },
      },
      inDialog: {
        type: Boolean,
        default: false,
      },
      contextmenuOption: {
        type: Object,
      },
      filter: {
        type: Boolean,
        default: true,
      },
      filterOption: {
        type: Object,
        default: () => {
          return {}
        },
      },
      withPageHeader: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        oneNodeHeight: 36,
        isFilterOnline: false,
        filterValue: '',
      }
    },
    methods: {
      // 缓存Tree
      getLocalTree() {
        return LocalTree[this.treeId]
      },
      setLocalTree(tree) {
        LocalTree[this.treeId] = tree
      },
      getNodeByKey(key) {
        return this.getLocalTree().getNodeByKey(key)
      },
      getTree() {
        return this.getLocalTree()
      },
      getRootNode() {
        return this.getLocalTree().getRootNode()
      },
      clearTree() {
        const tree = this.getLocalTree()
        tree.clear()
        tree.render(true, true)
      },
      removeNode(key) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }
        node.remove()
        this.updateViewport()
      },
      removeNodeChildren(key) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }
        node.removeChildren()
        this.updateViewport()
      },
      addNodeChildren(parentNode, children, pos) {
        if (!parentNode) {
          return
        }
        this.$nextTick(() => {
          this.updateViewport()
        })
        return parentNode.addChildren(children, pos)
      },
      sortChildren(sortMethod) {
        sortChildren(this.treeId, sortMethod)
      },
      renderTitle(key, title) {
        const node = this.getTree().getNodeByKey(key)
        if (!node) {
          return
        }
        node.title = title
        node.renderTitle()
        this.updateViewport()
      },
      toDictTree(srcTreeId = defaultTreeId, toDictCb) {
        return toDictTree(this.treeId, srcTreeId, toDictCb)
      },
      unselectableNodeByKey(key, status, redraw = false) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }

        node.unselectable = status || undefined
        node.unselectableStatus = status || undefined
        if (redraw) {
          this.updateViewport()
        }
      },

      setNodeSelected(key, status, redraw = false) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }
        node.setSelected(status)
        if (redraw) {
          this.$nextTick(() => {
            this.updateViewport()
          })
        }
      },
      selectAll(status = true) {
        this.getTree().selectAll(status)
      },
      expandAll(flag = true, opts = {}) {
        this.getTree().expandAll(flag, opts)
      },

      showOnlineDevices() {
        this.filterValue = ''
        this.isFilterOnline = true
        bftree.showOnlineDevices(this.treeId)
      },
      showAllDevices() {
        this.filterValue = ''
        this.isFilterOnline = false
        bftree.showAllDevices(this.treeId)
      },
      reFilterOnline() {
        // 如果fancytree处于文本过虑中，则按filter重新过滤
        if (this.filterValue) {
          this.filterNodes()
          return
        }

        // 处于在线过滤中，则重新过滤在线终端
        if (this.isFilterOnline) {
          this.showOnlineDevices()
        } else {
          this.showAllDevices()
        }
      },
      clearFilter() {
        this.filterValue = ''
        const tree = this.getLocalTree()
        tree && tree.clearFilter()
      },
      filterNodes() {
        const tree = this.getLocalTree()
        tree?.filterNodes(this.filterValue, tree.options.filter)
      },
      filterOnChange() {
        if (this.filterValue) {
          this.filterNodes()
        } else {
          this.clearFilter()
        }
        this.$nextTick(() => {
          this.$emit('filterValueChange', this.filterValue)
        })
      },
      showEntry(cmd, status = true) {
        $(this.tableTree).contextmenu('showEntry', cmd, status)
      },
      enableEntry(cmd, status = true) {
        $(this.tableTree).contextmenu('enableEntry', cmd, status)
      },
      replaceMenu(menu = []) {
        $(this.tableTree).contextmenu('replaceMenu', menu)
      },
      loadContextmenu() {
        if (!this.contextmenuOption) {
          return
        }
        $(this.tableTree).contextmenu({
          beforeOpen: this.inDialog
            ? (event, ui) => {
                // 找到dialog元素的z-index
                let el = event.target
                while (el) {
                  if (el.classList.contains('el-dialog__wrapper')) {
                    break
                  }
                  el = el.parentNode
                }
                const zIndex = +(el?.style.zIndex ?? 6000) + 2
                // 修正右键菜单的z-index，避免被dialog遮住
                if (ui.menu[0]) {
                  ui.menu[0].style.zIndex = zIndex
                }
              }
            : undefined,
          ...ContextmenuOption,
          ...this.contextmenuOption,
        })
      },
      getViewportCount() {
        if (!this.treeContainer) {
          return 0
        }
        return Math.floor(this.treeContainer.offsetHeight / this.oneNodeHeight)
      },
      updateTreeScrollbars(tree) {
        const scrollbar = tree.verticalScrollbar
        if (!scrollbar) {
          return
        }

        scrollbar.set(
          {
            start: tree.viewport.start,
            total: tree.visibleNodeList.length,
            visible: tree.viewport.count,
          },
          true
        )
      },
      async updateViewport(tree = this.getLocalTree(), resize = true) {
        if (!tree) {
          return
        }

        let start = tree.viewport.start
        if (isNaN(start)) {
          start = 0
        }
        const getCount = () => {
          return new Promise(resolve => {
            const entries = new Array(10).entries()
            const main = it => {
              if (it.done) {
                return 10
              }
              const count = this.getViewportCount()
              if (count > 0) {
                return resolve(count)
              }

              setTimeout(() => {
                main(entries.next())
              }, 100)
            }
            main(entries.next())
          })
        }
        const count = await getCount().catch(() => {})
        const data = {
          count,
          start,
        }
        this.setOption('viewport', data)
        tree.setViewport(data)
        if (resize) {
          this.$nextTick(() => {
            tree.redrawViewport(true)
          })
        }
      },
      initTree() {
        $(this.$refs.tableTree).fancytree({
          extensions: ['filter', 'grid'],
          checkbox: true,
          quicksearch: true,
          autoScroll: true,
          selectMode: 3,
          debugLevel: 0,
          // Defines what happens, when the user click a folder node.
          // 1:activate, 2:expand, 3:activate and expand, 4:activate/dblclick expands (default: 4)
          clickFolderMode: 1,
          filter: {
            autoApply: false, // 打开时是否自动加载数据
            autoExpand: true, // 父、子节点筛选
            counter: false, // 在图标处显示有几个匹配节点
            fuzzy: false, //  输入'fb' 匹配'FooBar'
            hideExpandedCounter: true, // 隐藏徽章
            hideExpanders: true, // 如果所有子节点都通过筛选隐藏，隐藏扩展
            highlight: true, // 高亮
            leavesOnly: false, // 只显示符合条件的节点
            nodata: true, // 没有符合条件的节点，不显示 nodata
            mode: 'hide', // "hide" or "dimm"
            ...this.filterOption,
          },
          sortChildren: true,
          strings: {
            noData: this.$t('dialog.noMatchText'),
          },
          nodata: false,
          source: [],
          table: {
            indentation: calcScaleSize(20),
            nodeColumnIdx: 0,
          },
          viewport: {
            enabled: true,
            count: this.getViewportCount(),
          },
          preInit: (event, data) => {
            const tree = data.tree
            tree.verticalScrollbar = new PlainScrollbar({
              numberOfItems: {
                start: 0,
                total: 100,
                visible: 10,
              },
              orientation: 'vertical',
              onSet: numberOfItems => {
                tree.setViewport({
                  start: Math.round(numberOfItems.start),
                  count: this.getViewportCount(),
                })
              },
              scrollbarElement: this.scrollBar,
            })
          },
          init: (event, data) => {
            data.tree.$container.addClass('fancytree-connectors')
            this.setLocalTree(data.tree)
            data.tree.adjustViewportSize()
            this.loadContextmenu()
            this.selectDefaultNode(this.selected)
            setTimeout(() => {
              this.updateTreeScrollbars(data.tree)
              this.$emit('loaded')
            }, 0)
          },
          updateViewport: (event, data) => {
            this.updateTreeScrollbars(data.tree)
          },
          select: (event, data) => {
            this.$emit('select', event, data)
          },
          dblclick: (event, data) => {
            this.$emit('dblclick', event, data)
          },
          click: (event, data) => {
            this.$emit('click', event, data)
          },
          ...this.option,
        })
      },
      setOption(optionName, value) {
        const tableTree = this.getTree()
        if (!tableTree) {
          return
        }

        tableTree.setOption(optionName, value)
      },
      selectDefaultNode(keys) {
        if (!Array.isArray(keys) || keys.length === 0) {
          return
        }
        for (let i = 0; i < keys.length; i++) {
          this.setNodeSelected(keys[i], true)
        }
        this.updateViewport()
      },
      initResizeObserver() {
        // 初始化node高度
        this.oneNodeHeight = calcScaleSize(36)
        resizeObserver = new ResizeObserver(() => {
          this.oneNodeHeight = calcScaleSize(36)
          this.getTree().setOption('indentation', calcScaleSize(20))
          this.getTree().getRootNode().render()
          console.log(
            'fancytree',
            this.getTree(),
            $.ui.fancytree._FancytreeClass.prototype,
            $.ui.fancytree._FancytreeNodeClass.prototype,
            this.getTree().getOption('indentation')
          )
        })

        resizeObserver.observe(document.body)
      },
    },
    beforeMount() {
      this.filterOnChange = debounce(this.filterOnChange, 200)
    },
    mounted() {
      this.initResizeObserver()
      this.initTree()
      bfglob.on('refilterOnlineDev', this.reFilterOnline)
      this.$nextTick(() => {
        this.updateViewport()
      })
    },
    beforeUnmount() {
      bfglob.off('refilterOnlineDev', this.reFilterOnline)
      resizeObserver?.disconnect()
    },
    watch: {
      filterValue(val) {
        if (val) {
          this.isFilterOnline = false
        }
      },
      isFilterOnline(val) {
        this.$emit('isFilterOnlineChange', val)
      },
      selected: {
        deep: true,
        handler(val) {
          this.selectDefaultNode(val)
        },
      },
      '$i18n.locale'() {
        this.loadContextmenu()
      },
      contextmenuOption: {
        deep: true,
        handler() {
          this.loadContextmenu()
        },
      },
    },
    computed: {
      tableTree() {
        return this.$refs.tableTree
      },
      treeContainer() {
        return this.$refs.fancytreeGridContainer
      },
      scrollBar() {
        return this.$refs.scrollBar
      },
      filterInput() {
        return this.$refs.filterInput
      },
      scrollBarHeight() {
        if (this.filter && this.filterInput) {
          return this.filterInput.offsetHeight
        }

        return 0
      },
      nodeNumbers() {
        return this.getTree()?.count()
      },
    },
    components: {
      treeToolbar: defineAsyncComponent(() => import('./treeToolbar')),
      treeFilterInput: defineAsyncComponent(() => import('./TreeFilterInput')),
    },
  }
</script>

<style lang="scss">
  @use '@/assets/bfdxFont/iconfont.css';
  @import url('jquery-ui/themes/base/all.css');

  .fancytree-grid {
    &.has-filter .fancytree-grid-wrapper {
      height: calc(100% - 44px);
      margin-top: 7px;
    }
    &.with-page-header .fancytree-grid-container {
      height: calc(100% - 36px);
    }

    .fancytree-grid-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.22), transparent);
      border-width: 1px;
      border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;
      height: 100%;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 4, 0.27);
        pointer-events: none;
        filter: blur(200px);
      }
    }
    .fancytree-grid-container {
      height: 100%;
      padding-right: 10px;
    }

    .fancytree-node mark,
    .fancytree-node .mark {
      padding: unset;
      background-color: #ffeb3b;
    }

    table.fancytree-container.fancytree-ext-table {
      outline: none;
      width: 100%;
      height: 100%;
      display: flex;
      position: relative;
      overflow-x: auto;
      overflow-y: hidden;
      font-size: 12px;
      font-family: 'AlibabaPuHuiTi2';

      thead tr th,
      tbody tr td {
        text-align: left;
      }

      tbody {
        padding-right: 8px;
      }

      tbody tr {
        &.fancytree-exp-n span.fancytree-node,
        &.fancytree-exp-nl span.fancytree-node {
          position: relative;
          .fancytree-expander::after {
            content: '';
            width: 16px;
            height: 1px;
            position: absolute;
            top: 14px;
          }
          &::before {
            content: '';
            width: 33px;
            height: 36px;
            position: absolute;
            bottom: 15px;
            background:
              linear-gradient(rgba($color: #7e8fa6, $alpha: 0.52), rgba($color: #7e8fa6, $alpha: 0.52)) 7px 0px/1px 100% no-repeat,
              repeating-linear-gradient(to right, rgba($color: #7e8fa6, $alpha: 0.52) 2px, rgba($color: #7e8fa6, $alpha: 0) 4px) 7px 100%/14px 1px no-repeat;
            // border-bottom: 1px dashed rgba($color: #7e8fa6, $alpha: 0.52);
          }
        }

        &:hover,
        &.fancytree-active {
          background-color: transparent;
          outline: none;
        }

        & td {
          border: none;
          padding: 3px 0;
        }
        span.fancytree-node {
          line-height: 20px;
          white-space: nowrap;
        }
        span.fancytree-node:hover span.fancytree-title {
          background-color: #00355b;
        }

        span.fancytree-expander,
        span.fancytree-checkbox {
          @extend .bf-iconfont;
          position: relative;
          margin: 0 3px 0 0;
          width: 16px;
          height: 30px;
          line-height: 30px;
          font-size: 9px;
          text-align: center;
          color: #1a7aff;
          background-image: none;
          background-position: unset;
        }

        &.fancytree-has-children.fancytree-folder span.fancytree-expander {
          @extend .bfdx-shouqi;
          color: #6b7078;
        }

        &.fancytree-has-children.fancytree-folder.fancytree-expanded span.fancytree-expander {
          @extend .bfdx-zhankai;
          color: #1a7aff;
        }

        &.fancytree-selected span.fancytree-checkbox {
          @extend .bfdx-shuzhuang;
        }

        span.fancytree-checkbox {
          // TODO: 复选框未选中状态的图标
          @extend .bfdx-jiqun;
        }

        span.fancytree-title {
          margin: 0;
          border-radius: 5px;
          color: #fff;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          height: 30px;

          .allDevCount {
            margin-left: 10px;
            height: 15px;
            padding: 0 5px;
            background-color: #1a7aff;
            border-radius: 4px;
            font-size: 11px;
            line-height: 15px;
          }
        }

        &.fancytree-selected {
          background-color: transparent;
        }

        &.fancytree-active:hover,
        &.fancytree-selected:hover {
          background-color: transparent;
          outline: none;
        }

        &.fancytree-focused span.fancytree-title {
          outline: none;
        }

        &.fancytree-active span.fancytree-title:hover,
        &.fancytree-selected .fancytree-title:hover,
        &.fancytree-focused span.fancytree-title {
          color: inherit;
          background: #00355b;
        }
      }
    }

    .fancytree-ext-table-scroll-bar.scrollbar-vertical {
      width: 10px;
      background-color: transparent;
      &.plain-scrollbar[data-scrollable='false'] {
        visibility: hidden;
      }

      .arrow-up,
      .arrow-down {
        display: none;
      }

      .slider-area {
        top: 0;
        bottom: 0;
        width: 100%;

        .slider {
          width: 100%;
          border-radius: 4px;
          background-color: #0b3b7d;
          box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1);

          &:hover,
          &:focus {
            box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1);
          }

          &:active {
            opacity: 0.5;
          }
        }
      }
    }
  }
</style>
