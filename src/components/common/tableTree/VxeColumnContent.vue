<script setup lang="ts">
  import { TreeNodeData } from 'element-plus'
  import { TreeNodeType } from './types'
  import { computed, useTemplateRef } from 'vue'
  import { getDeviceNodeTooltip, getOrgNodeTooltip, getOrgNodeIcon, getDeviceStatusClassName, getDeviceChannel, getDeviceUserName } from './treeUtil'

  const {
    enableCheckbox = true,
    row,
    checked = true,
    indeterminate,
    toggleCheckboxEvent,
    showDeviceChannel = true,
    showDeviceUserName = true,
  } = defineProps<{
    enableCheckbox?: boolean
    row: TreeNodeData
    checked?: boolean
    indeterminate?: boolean
    showDeviceChannel?: boolean
    showDeviceUserName?: boolean
    toggleCheckboxEvent: (row: TreeNodeData) => void
  }>()

  const getNodeOriginData = (row: TreeNodeData) => {
    if (row.nodeType === TreeNodeType.Org) {
      return bfglob.gorgData.get(row.rid)
    } else {
      return bfglob.gdevices.get(row.rid)
    }
  }
  const contextRef = useTemplateRef('contextRef')
  const isOverflow = computed(() => {
    const el = contextRef.value
    if (!el) return false
    return el.scrollWidth > el.clientWidth
  })

  const displayData = computed(() => {
    const originData = getNodeOriginData(row)
    const isOrgNode = row.nodeType === TreeNodeType.Org
    const orgIcon = isOrgNode ? getOrgNodeIcon(originData) : ''
    const deviceIcon = isOrgNode ? '' : getDeviceStatusClassName(originData)
    const channel = isOrgNode ? '' : getDeviceChannel(originData)
    const userName = isOrgNode ? '' : getDeviceUserName(originData)

    const deviceContent = originData.selfId + (showDeviceChannel ? channel : '') + (showDeviceUserName ? userName : '')
    return {
      tooltipContent: isOrgNode ? getOrgNodeTooltip(originData) : getDeviceNodeTooltip(originData),
      iconClass: isOrgNode ? orgIcon : deviceIcon,
      showIcon: isOrgNode ? !!orgIcon : deviceIcon !== 'device_status_none',
      content: (isOrgNode ? originData.orgShortName : deviceContent) as string,
    }
  })
</script>

<template>
  <div class="vxe-column-content-wrapper relative h-[20px] w-full inline-flex gap-[5px]">
    <span v-if="enableCheckbox" class="vxe-column-checkbox" @click.stop="toggleCheckboxEvent(row)">
      <i v-if="indeterminate" class="bf-iconfont bfdx-bufenxuanzhong"></i>
      <i v-else-if="checked" class="bf-iconfont bfdx-fuxuanduoanniu"></i>
      <i v-else class="bf-iconfont bfdx-dingweimaodianwaikuang"></i>
    </span>
    <el-tooltip
      popper-class="bf-tooltip"
      effect="dark"
      placement="bottom"
      :show-after="1000"
      :content="isOverflow ? displayData.tooltipContent + '...' : displayData.tooltipContent"
    >
      <div ref="contextRef" class="vxe-column-content inline-flex justify-start items-center">
        <i
          v-show="displayData.showIcon"
          :class="['relative bf-iconfont mr-[5px] w-[20px] h-[20px] before:text-[20px] text-[#1A7AFF]', displayData.iconClass]"
        ></i>
        {{ displayData.content }}
        <div
          v-if="row.nodeType === TreeNodeType.Org && getNodeOriginData(row)?.sumDeviceCount > 0"
          class="h-[15px] my-auto ml-[5px] leading-[15px]! px-[5px] bg-[#1a7aff] rounded-[4px] text-[11px]"
        >
          {{ getNodeOriginData(row)?.sumDeviceCount }}
        </div>
      </div>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
  @use '@/assets/bfdxFont/iconfont.css';
  .vxe-column-checkbox {
    * {
      margin: auto 0 auto 8px;
      color: #1a7aff;
      cursor: pointer;
      height: 20px;
      width: 20px;
      font-size: 20px;
    }
  }

  .vxe-column-content {
    padding: 0 5px;
    cursor: default;
  }

  $device_status_color_light_gray: rgb(123, 144, 161, 0.7);
  $device_status_color_gray: #949494;
  $device_status_color_yellow: #ffff1e;
  $device_status_color_green: #2fffa2;
  $device_status_color_red: #ff5833;
  $device_status_color_blue: rgb(26, 122, 255);

  @mixin device_status_base {
    @extend .bfdx-zhuangtaitubiao2;
    color: $device_status_color_gray;
  }

  .device_status {
    &_none {
      @include device_status_base;
      display: none;
    }

    &_yellow {
      @include device_status_base;
      color: $device_status_color_yellow;
    }

    &_light_gray {
      @include device_status_base;
      color: $device_status_color_light_gray;
    }

    &_gray {
      @include device_status_base;
    }

    &_green {
      @include device_status_base;
      color: $device_status_color_green;
    }

    &_red {
      @include device_status_base;
      color: $device_status_color_red;
    }

    &_other_red {
      @include device_status_base;

      &:after {
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: absolute;
        top: 4px;
        right: 4px;
        z-index: 10;
        background-color: $device_status_color_red;
      }
    }

    &_icon_th {
      @extend .bfdx-tonghuazhong;
      color: $device_status_color_blue;
    }

    &_emergency_green {
      @include device_status_base;
      color: $device_status_color_green;

      animation: _emergency_green 0.5s infinite;
    }
    @keyframes _emergency_green {
      0% {
        color: $device_status_color_green;
      }
      50% {
        color: $device_status_color_red;
      }
      100% {
        color: $device_status_color_green;
      }
    }

    &_emergency_yellow {
      @include device_status_base;

      color: $device_status_color_yellow;
      animation: _emergency_yellow 0.5s infinite;
    }
    @keyframes _emergency_yellow {
      0% {
        color: $device_status_color_yellow;
      }
      50% {
        color: $device_status_color_red;
      }
      100% {
        color: $device_status_color_yellow;
      }
    }
  }
</style>
