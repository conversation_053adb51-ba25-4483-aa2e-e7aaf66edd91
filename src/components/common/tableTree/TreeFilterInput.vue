<script setup lang="ts">
  import { computed, h } from 'vue'

  const { showIcon = false, isInPageHeader = false } = defineProps<{
    showIcon?: boolean
    isInPageHeader?: boolean
  }>()

  const icon = computed(() => {
    if (!showIcon) return ''
    return () => h('i', { class: 'bf-iconfont bfdx-sousuo' })
  })

  const model = defineModel<string>()
</script>

<template>
  <el-input
    ref="filterInput"
    v-model="model"
    :placeholder="$t('tree.filter')"
    :prefix-icon="icon"
    :class="['table-tree-filter-input', { 'in-page-header': isInPageHeader }]"
  />
</template>

<style lang="scss">
  .table-tree-filter-input {
    justify-content: center;
    .el-input__wrapper {
      background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0));
      box-shadow: none;
      padding: 0;
      font-size: 16px;

      .el-input__prefix {
        color: #fff;
        line-height: 24px;
      }

      .el-input__inner,
      .el-input__inner::placeholder {
        text-align: center;
        color: #fff;
        line-height: 24px;
      }
    }

    &.in-page-header {
      .el-input__wrapper {
        background: transparent;
        height: 32px;
        font-size: 24px;
        font-weight: bold;

        .el-input__prefix {
          .el-icon {
            width: 24px;
            height: 24px;
          }
          color: #faffff;
        }

        .el-input__inner,
        .el-input__inner::placeholder {
          text-align: left;
          height: 32px;
          line-height: 32px;
          color: #faffff;
        }
      }
    }
  }
</style>
