<template>
  <!-- 根元素：相对定位，并监听鼠标进入和离开事件 -->
  <div class="relative inline-block" @mouseenter="isMenuVisible = true" @mouseleave="isMenuVisible = false">
    <img src="@/assets/images/loginBg/switch_langs.svg" class="cursor-pointer self-end" />

    <transition
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0 translate-y-2"
      leave-active-class="transition-all duration-150 ease-in"
      leave-to-class="opacity-0 translate-y-2"
    >
      <div
        v-show="isMenuVisible"
        class="absolute z-[1000] top-full left-1/2 -translate-x-1/2 mt-2 w-[120px] box-border pt-6 px-3 pb-3 bg-no-repeat bg-[length:100%_100%] bg-[url('@/assets/images/loginBg/1x/list_dlg.webp')]"
      >
        <!-- 列表容器 -->
        <ul class="list-none m-0 p-0 h-full overflow-y-auto">
          <li
            v-for="lang in languageList"
            :key="lang.code"
            class="text-white text-center text-base py-2 rounded-md cursor-pointer transition-colors hover:bg-white/20"
            :class="{ '!text-[#FF811D] font-bold': lang.code === currentLang }"
            @click="selectLanguage(lang.code)"
          >
            {{ lang.name }}
          </li>
        </ul>
      </div>
    </transition>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { SupportedLangList, getDisplayLabel, loadLanguageAsync } from '@/modules/i18n'
  import bfutil from '@/utils/bfutil'
  import i18n from '@/modules/i18n'

  const isMenuVisible = ref(false)
  const currentLang = ref(i18n.global.locale.value)

  // 监听 i18n 语言变化，同步更新当前语言
  watch(
    () => i18n.global.locale.value,
    newLang => {
      currentLang.value = newLang
    }
  )

  const languageList = computed(() => {
    return SupportedLangList.map(lang => {
      return {
        code: lang,
        name: getDisplayLabel(lang),
      }
    })
  })

  const emit = defineEmits(['change'])

  const selectLanguage = langCode => {
    isMenuVisible.value = false
    emit('change', langCode)
    if (currentLang.value === langCode) return

    // 清理可能存在的 vue-i18n 自动保存的语言设置
    try {
      localStorage.removeItem('i18n/i18n-locale')
    } catch (_e) {
      // 忽略清理错误
    }

    loadLanguageAsync(langCode)
      .then(() => {
        // 语言切换成功后保存到本地存储
        bfutil.saveLang(langCode)
        // 手动触发响应式更新
        currentLang.value = langCode
        console.log('Language switched to:', langCode)
      })
      .catch(err => {
        console.error('Language switch failed:', err)
      })
  }
</script>

<style scoped>
  ul::-webkit-scrollbar {
    width: 4px;
  }
  ul::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
</style>
