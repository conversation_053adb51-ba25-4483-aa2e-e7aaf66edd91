<template>
  <BfDialog
    v-model="sipGatewayVisible"
    :title="$t('dialog.sipGatewayConfig')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="header-border footer-border phone-gateway-info-dialog"
    @close="onClose"
    center
  >
    <el-form
      ref="formData"
      :model="formData"
      :rules="rules"
      label-width="130px"
      :validate-on-rule-change="false"
      class="grid grid-cols-1 controller-sip-gateway-config"
    >
      <el-form-item>
        <BfCheckbox v-model="formData.runBy8100" class="sip-runBy8100" @change="runBy8100Change">
          {{ $t('dialog.sipProxyGateway') }}
        </BfCheckbox>
      </el-form-item>
      <el-form-item ref="hostItem" prop="host" class="form-item-ellipsis">
        <template #label>
          <EllipsisText :content="$t('dialog.sipDomain') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInput v-model="formData.host" maxlength="56" style="height: 50px" />
      </el-form-item>
      <el-form-item ref="portItem" prop="port">
        <template #label>
          <EllipsisText :content="$t('dialog.sipPort') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInputNumberV2 v-model.number="formData.port" class="!w-full" :min="0x01" :max="0xffff" step-strictly />
      </el-form-item>
      <el-form-item prop="sipNo">
        <template #label>
          <EllipsisText :content="$t('dialog.sipNo') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInput v-model="formData.sipNo" :maxlength="16" style="height: 50px" />
      </el-form-item>
      <el-form-item prop="password">
        <template #label>
          <EllipsisText :content="$t('dialog.sipPassword') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInput v-model="formData.password" type="password" show-password :maxlength="16" style="height: 50px" />
      </el-form-item>
      <el-form-item prop="soundLocale" class="form-item-ellipsis">
        <template #label>
          <EllipsisText :content="$t('dialog.sipSoundLocale') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfSelect
          v-model="formData.soundLocale"
          class="!h-[50px]"
          :placeholder="$t('dialog.select')"
          filterable
          clearable
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option v-for="item in soundLocaleTypes" :key="item.value" :label="item.label" :value="item.value" />
        </BfSelect>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="text-center flex justify-center gap-3">
        <BfButton color-type="primary" @click="onConfirm">
          {{ $t('dialog.confirm') }}
        </BfButton>
        <BfButton color-type="info" @click="onReset">
          {{ $t('dialog.reset') }}
        </BfButton>
      </div>
    </template>
  </BfDialog>
</template>

<script>
  import validateRules from '@/utils/validateRules'
  import { SoundLocales, DefaultFormData, DefaultFormData as DefaultSipFormData } from './common'
  import BfDialog from '@/components/bfDialog/main'
  import BfButton from '@/components/bfButton/main'
  import BfInput from '@/components/bfInput/main'
  import BfInputNumberV2 from '@/components/bfInputNumber/main'
  import BfSelect from '@/components/bfSelect/main'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'

  export default {
    name: 'SipGatewaySettings',
    emits: ['update:modelValue', 'update:visible'],
    components: {
      BfDialog,
      BfButton,
      BfInput,
      BfInputNumberV2,
      BfSelect,
      BfCheckbox,
      EllipsisText,
    },
    props: {
      visible: {
        type: Boolean,
        required: true,
      },
      modelValue: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        formData: {
          ...DefaultFormData,
        },
      }
    },
    methods: {
      runBy8100Change(value) {
        // 重置表单校验规则
        if (!value) {
          this.$refs.formData?.clearValidate()
        }
      },
      onClose() {
        this.sipGatewayVisible = false
      },
      async validate() {
        return await this.$refs.formData.validate().catch(() => false)
      },
      async onConfirm() {
        const valid = await this.validate()
        if (!valid) {
          return
        }

        this.$emit('update:modelValue', this.formData)
        this.onClose()
      },
      onReset() {
        this.formData = {
          ...DefaultSipFormData,
        }
        this.$nextTick(() => {
          this.$refs.formData?.clearValidate()
        })
      },
    },
    computed: {
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      sipGatewayVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      rules() {
        if (!this.formData.runBy8100) {
          return {}
        }

        return {
          sipNo: [validateRules.required(), validateRules.mustNumber()],
          password: [validateRules.required()],
          host: [validateRules.required(), validateRules.checkHost()],
          port: [validateRules.required()],
          // port: [validateRules.required(), validateRules.range('blur', 1, 0xFFFF)],
          soundLocale: [validateRules.required()],
        }
      },
      soundLocaleTypes() {
        return [
          { label: this.$t('header.CN'), value: SoundLocales.ZH },
          { label: this.$t('header.EN'), value: SoundLocales.EN },
        ]
      },
    },
    watch: {
      modelValue: {
        handler(val) {
          this.formData = val
        },
        immediate: true,
        deep: true,
      },
    },
  }
</script>

<style lang="scss">
  .el-dialog.phone-gateway-info-dialog {
    width: 520px;
  }
</style>
