<template>
  <div class="trackCtrol">
    <el-button-group>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('map.slowDown')" placement="top" :enterable="false">
        <el-button type="primary" class="slow" :disabled="slowest" @click="fastForward(false)" />
      </el-tooltip>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('map.fastForward')" placement="top" :enterable="false">
        <el-button type="primary" class="fast" :disabled="fastest" @click="fastForward(true)" />
      </el-tooltip>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="playTitle" placement="top" :enterable="false">
        <el-button type="primary" :class="isplay ? 'stop' : 'play'" @click="playAnimate" />
      </el-tooltip>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('map.stop')" placement="top" :enterable="false">
        <el-button type="primary" class="over" @click="stopAnimate" />
      </el-tooltip>
    </el-button-group>
    <el-slider v-model="sliderValue" :max="sliderMax" :min="1" />
  </div>
</template>

<script>
  export default {
    name: 'TrackCtrol',
    emits: ['timeout', 'play', 'stop', 'sliderChange'],
    props: {
      sliderMax: {
        type: Number,
        default: 1,
      },
      end: Boolean,
      sliderVal: Number,
    },
    data() {
      return {
        sliderValue: 1,
        isplay: false,
        timeout: 1000,
      }
    },
    methods: {
      fastForward(isFast) {
        if (isFast) {
          if (this.timeout > 100) {
            this.timeout -= 100
          }
        } else {
          if (this.timeout < 2000) {
            this.timeout += 100
          }
        }
        this.$emit('timeout', this.timeout)
      },
      playAnimate() {
        this.isplay = !this.isplay
        this.$emit('play', this.isplay)
      },
      stopAnimate() {
        this.$emit('stop')
      },
    },
    watch: {
      sliderValue(val) {
        // 发布slider值更新消息，同步地图图层popup等信息
        this.$emit('sliderChange', val - 1)
      },
      end(val) {
        // 监听动画播放结束
        if (val) {
          this.sliderValue = 1
          this.isplay = false
        }
      },
      sliderVal(val) {
        // 接收新的slider值
        this.sliderValue = ++val
      },
    },
    mounted() {},
    computed: {
      playTitle() {
        return this.isplay ? this.$t('map.pased') : this.$t('map.play')
      },
      fastest() {
        return this.timeout === 100
      },
      slowest() {
        return this.timeout === 2000
      },
    },
  }
</script>

<style lang="scss">
  .trackCtrol {
    position: absolute;
    top: 10px;
    left: 20px;
    max-width: 200px;
    z-index: 1000;
  }

  .trackCtrol button {
    background-color: transparent;
    border: none;
    width: 44px;
    height: 44px;

    &.slow {
      background-image: url('@/assets/images/dispatch/map/play_slow.svg');
      background-size: 100% 100%;
    }

    &.fast {
      background-image: url('@/assets/images/dispatch/map/play_fast.svg');
      background-size: 100% 100%;
    }

    &.play {
      background-image: url('@/assets/images/dispatch/map/start_trace.svg');
      background-size: 100% 100%;
    }

    &.stop {
      background-image: url('@/assets/images/dispatch/map/stop_trace.svg');
      background-size: 100% 100%;
    }

    &.over {
      background-image: url('@/assets/images/dispatch/map/over_trace.svg');
      background-size: 100% 100%;
    }
    &:hover {
      background-color: transparent;
    }
    &:active {
      scale: 0.96;
    }
  }

  .trackCtrol {
    .el-slider__runway {
      background: rgba(208, 233, 255, 0.6);
    }
    .el-slider__bar {
      // background: linear-gradient(90deg, #90c9ff 0%, #318fff 100%);
      background-color: #318fff;
    }
  }
</style>
