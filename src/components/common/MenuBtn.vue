<template>
  <div class="menu-item-wrapper h-[72px] flex items-center">
    <div
      :class="[
        'menu-button relative flex items-center cursor-pointer select-none pl-[10px]',
        'transition-all duration-300 ease-in-out',
        'active:scale-98',
        { active: isActive },
        isActive ? 'w-[252px] h-[72px]' : 'w-[210px] h-[60px]',
      ]"
      @click="handleClick"
    >
      <div
        :style="{ backgroundImage: iconUrl }"
        :class="[
          'icon-display',
          'relative z-10 w-[41px] h-[31px] mr-[4px] transition-all duration-300 ease-in-out',
          { 'transform -translate-y-[4.5px]': isActive },
        ]"
      />
      <EllipsisText
        :class="[
          'relative z-10 text-white font-bold [text-shadow:1px_1px_2px_rgba(0,0,0,0.5)] transition-all duration-300 ease-in-out !mb-0',
          { 'transform -translate-y-[4.5px]': isActive },
        ]"
        :style="{
          fontSize: `${isActive ? (navItemConfig?.activeFontSize ?? 26) : (navItemConfig?.fontSize ?? 21)}px`,
          width: isActive ? '150px' : '120px',
        }"
        :content="unref(navItem.meta?.navItemConfig).label"
      ></EllipsisText>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onUnmounted, effect, unref } from 'vue'
  import EllipsisText from './EllipsisText.vue'
  import { RouteRecordRaw } from 'vue-router'

  const props = defineProps<{
    navItem: RouteRecordRaw
    isActive: boolean
  }>()

  const emit = defineEmits(['click'])

  // '@/assets/images/(manage|dispatch)/**/*' 多级目录导入打包
  const imgs = import.meta.glob('@/assets/images/(manage|dispatch)/**/*', { eager: true })

  Object.keys(imgs).forEach(key => {
    const k = key.replace(/\/src\/assets\//, '')
    imgs[k] = imgs[key]
    delete imgs[key]
  })

  const iconPath = computed(() => {
    const config = props.navItem.meta?.navItemConfig.value
    console.log('config', config)
    return props.isActive ? config.activeIconPath : config.inactiveIconPath
  })

  const navItemConfig = computed(() => {
    return unref(props.navItem.meta?.navItemConfig)
  })

  const iconUrl = ref('')

  const s = effect(async () => {
    const img = imgs[iconPath.value]
    if (!img) {
      return
    }
    iconUrl.value = `url(${img.default ?? img})`
  })

  onUnmounted(() => {
    s.effect.stop()
  })

  const handleClick = () => {
    emit('click', props.navItem)
  }
</script>

<style scoped>
  .menu-button {
    background-image: url('@/assets/images/manage/menu_btn.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-color: transparent;
    /* 明确告诉浏览器哪些属性需要过渡效果 */
    transition-property: width, height, transform;
  }
  .menu-button.active {
    background-image: url('@/assets/images/manage/menu_btn_selected.svg');
  }

  .icon-display {
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    background-color: transparent;
  }
</style>
