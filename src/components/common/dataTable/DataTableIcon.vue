<template>
  <span
    class="relative inline-flex justify-center items-center before:text-[#1286DE] before:text-[15px] bg-white rounded-[2px] size-[25px] aspect-square"
    :class="iconClass"
  ></span>
</template>

<script lang="ts" setup>
  const { icon = 'bfdx-duijiangji' } = defineProps<{
    // 图标字体类名,用于iconfont图标
    icon?: string
  }>()

  const iconClass = icon.includes('iconfont') ? icon : `bf-iconfont ${icon}`
</script>
