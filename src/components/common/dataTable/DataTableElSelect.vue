<!-- 二次封装的el-select,修改默认样式，其他属性保持不变 -->
<script setup lang="ts">
  import type { SelectInstance, SelectProps as ElSelectProps } from 'element-plus'
  import { ElSelect } from 'element-plus'
  import DataTableRowItem from './DataTableRowItem.vue'
  import { ExtractPublicPropTypes, mergeProps, ref } from 'vue'
  import 'element-plus/es/components/select/style/css'

  //  定义插槽类型，导出给父组件使用
  type SelectSlot = InstanceType<typeof ElSelect>['$slots']
  defineSlots<SelectSlot>()

  // 导出原有属性，供父组件传入
  type SelectProps = ExtractPublicPropTypes<ElSelectProps> & {
    prefixIconFont?: string
  }
  const props = withDefaults(defineProps<SelectProps>(), {
    clearable: true,
    filterable: true,
    prefixIconFont: 'bfdx-zhongduan',
    effect: 'dark',
    placement: 'bottom',
    popperClass: 'data-table-el-select-popper',
  })

  // 暴露el-select ref给外部
  const rawRef = ref<SelectInstance | null>(null)
  defineExpose<SelectInstance>(
    new Proxy(
      {},
      {
        get: (_target, key) => rawRef.value?.[key as keyof SelectInstance],
        has: (_target, key) => key in (rawRef.value || {}),
      }
    ) as SelectInstance
  )

  // 自定义点击事件处理函数
  const handleClick = () => {
    rawRef.value?.toggleMenu()
    rawRef.value?.focus()
  }
</script>

<template>
  <DataTableRowItem class="data-table-el-select__wrapper" :iconFont="props.prefixIconFont" @click="handleClick">
    <el-select v-bind="mergeProps($attrs, props)" ref="rawRef">
      <template v-for="(_, slotName) in $slots" #[slotName]="scope">
        <slot :name="slotName" v-bind="{ ...scope }" />
      </template>
    </el-select>
  </DataTableRowItem>
</template>

<style lang="scss" scoped>
  .data-table-row-item {
    &:has(.el-select) {
      cursor: text;
    }
    .data-table-row-item__content .el-select {
      position: relative;
      color: #dde6f6;
      :deep(.el-select__wrapper) {
        background-color: transparent;
        box-shadow: none;
        min-height: auto;
        padding: 0;
        font-size: 13px;
        height: 29px;
        &:not(.is-focused) {
          box-shadow: none;
        }
        .el-select__input {
          color: #dde6f6;

          width: 123px;
        }
        .el-select__placeholder {
          color: #dde6f6;
        }
        .el-icon.el-select__icon {
          color: #7fb5e5;
        }
      }
    }
  }
</style>

<style lang="scss">
  .data-table-el-select-popper {
    &.el-popper {
      background-image: linear-gradient(#012c4a, #012c4a), linear-gradient(to bottom, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
      background-clip: padding-box, border-box;
      background-origin: border-box;
      border: 1px solid transparent;
      background-color: transparent;
      border-radius: 10px;
    }
    .el-popper__arrow {
      &::before {
        background: #012c4a !important;
        clip-path: polygon(0 0, 0% 100%, 100% 0);
        border: 1px solid rgba(80, 213, 255, 0.63) !important;
      }
    }

    .el-select-dropdown__item {
      color: #fff;
      font-size: 16px;
      text-align: center;
      vertical-align: middle;
      margin: 0 10px;
      border-radius: 3px;
      padding: 0 10px;
      &.is-hovering {
        background: #01375c;
      }
    }
  }
</style>
