<template>
  <div
    class="data-table-row-item cursor-pointer relative isolate flex justify-center items-center gap-[5px] h-[35px] p-[2.5px] pl-[5px] pb-[3px] rounded-[2px]! bg-gradient-to-r from-[#0e85ba] to-[#001024] after:content-[''] after:-z-1 after:absolute after:top-0 after:left-0 after:right-0 after:bottom-[1px] after:pointer-events-none after:bg-[#104cba] after:rounded-[2px] hover:after:bg-[#508EFF] focus:opacity-70 active:opacity-70 transition duration-100 ease-in-out flex-auto"
    :class="{ disabled: !enable }"
  >
    <img v-if="iconSrc.length > 0" :src="iconSrc" class="relative size-[25px] aspect-square" />
    <DataTableIcon v-if="iconFont.length > 0" :icon="iconFont" />
    <div
      class="data-table-row-item__content relative text-[13px] text-white font-medium text-center align-middle h-[29px] py-[2px] px-[9px] bg-[#000]/30 flex items-center justify-center w-full"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  const {
    iconSrc = '',
    iconFont = '',
    enable = true,
  } = defineProps<{
    // 图标的URL,用于img标签
    iconSrc?: string
    // 图标字体类名,用于iconfont图标
    iconFont?: string
    enable?: boolean
  }>()
</script>

<style lang="scss" scoped>
  .disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  .data-table-row-item__content {
    background-image:
      linear-gradient(to right, rgba(229, 244, 255, 0.67), rgba(229, 244, 255, 0.67)),
      linear-gradient(to right, rgba(229, 244, 255, 0.67), rgba(229, 244, 255, 0.67)),
      linear-gradient(to right, rgba(229, 244, 255, 0.67), rgba(229, 244, 255, 0.67)),
      linear-gradient(to right, rgba(229, 244, 255, 0.67), rgba(229, 244, 255, 0.67));
    background-size:
      1.5px 1.5px,
      1.5px 1.5px,
      1.5px 1.5px,
      1.5px 1.5px;
    background-repeat: no-repeat;
    background-position:
      top left,
      top right,
      bottom left,
      bottom right;
  }
</style>
