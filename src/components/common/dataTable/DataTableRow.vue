<template>
  <div :class="['dataTableFormHead flex justify-between m-[15px] text-white gap-y-[10px]', props.wrap ? 'item-stretch' : 'items-center flex-wrap']">
    <div :class="['flex justify-between gap-[10px] mr-auto max-md:flex-auto', props.wrap ? 'items-stretch' : 'items-center']">
      <slot name="left"></slot>
    </div>

    <div :class="['flex justify-between  gap-[10px] ml-auto max-md:flex-auto', props.wrap ? 'items-end' : 'items-center']">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const props = withDefaults(
    defineProps<{
      wrap: boolean
    }>(),
    {
      wrap: false,
    }
  )
</script>
