<template>
  <el-row :gutter="20" class="no-margin-x timezone-wrapper" type="flex" align="middle">
    <el-divider v-if="divider">
      <el-icon>
        <CaretBottom />
      </el-icon>
      <span v-text="$t('dialog.timeSetting')" />
    </el-divider>
    <el-col :xs="24" :sm="12">
      <el-form-item :label="$t('dialog.timeZoneHours')">
        <el-input-number v-model="hoursDiff" :min="minHour" :max="maxHour" :step="1" :disabled="!customTimezone" @change="calcTimezoneDiff" />
      </el-form-item>
    </el-col>
    <el-col :xs="24" :sm="12">
      <el-form-item :label="$t('dialog.timeZoneMinutes')">
        <el-input-number v-model="timezone.minutesDiff" :min="0" :max="59" :step="1" :disabled="!customTimezone" @change="calcTimezoneDiff" />
      </el-form-item>
    </el-col>
    <el-col v-if="showTimezoneList" :xs="24" :sm="12">
      <el-form-item :label="$t('dialog.timeZone')">
        <el-select
          v-model="timezone.timezoneId"
          filterable
          :placeholder="$t('dialog.timeZone')"
          :no-match-text="$t('dialog.noMatchText')"
          @change="selectTimeZone"
        >
          <el-option v-for="(item, index) in timeZoneList" :key="index" :label="item.label" :value="item.timezoneId" />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :xs="24" :sm="12">
      <el-form-item :label="$t('dialog.deviceTime')" class="sync-timezone-container">
        <span class="timezone-label" v-text="datetimeString" />
        <el-tooltip popper-class="bf-tooltip" :content="$t('writeFreq.syncTimezone')" :open-delay="1000" placement="top">
          <el-button circle type="primary" class="sync-button" icon="refresh" @click="syncTimezone" />
        </el-tooltip>
      </el-form-item>
    </el-col>
  </el-row>
</template>

<script>
  import TimezoneList from '@/utils/timezoneList'

  const TimezoneOffset = new Date().getTimezoneOffset()

  function getLocalDateTime(datetime) {
    return {
      year: datetime.getFullYear(),
      month: datetime.getMonth() + 1,
      date: datetime.getDate(),
      hours: datetime.getHours(),
      minutes: datetime.getMinutes(),
      seconds: datetime.getSeconds(),
    }
  }

  function getHoursDiff(offset = TimezoneOffset) {
    return (Math.abs(offset) / 60) | 0
  }

  function getMinutesDiff(offset = TimezoneOffset) {
    return Math.abs(offset) % 60
  }

  function getGMTStandard(offset = TimezoneOffset) {
    return offset < 0 ? 1 : 0
  }

  function decodeTimezoneOffset(offset = TimezoneOffset) {
    return {
      hoursDiff: getHoursDiff(offset),
      minutesDiff: getMinutesDiff(offset),
      gmtStandard: getGMTStandard(offset),
    }
  }

  export default {
    name: 'Td930Timezone',
    emits: ['update:modelValue'],
    props: {
      modelValue: {
        type: Object,
        required: true,
      },
      divider: {
        type: Boolean,
        default: true,
      },
      showTimezoneList: {
        type: Boolean,
        default: true,
      },
      maxHour: {
        type: Number,
        default: 12,
      },
      minHour: {
        type: Number,
        default: -12,
      },
    },
    data() {
      return {
        timezone: {
          ...getLocalDateTime(new Date()),
          ...decodeTimezoneOffset(TimezoneOffset),
          timezoneId: 21,
        },
        timer: null,
        hoursDiff: 8,
      }
    },
    computed: {
      timezoneSign() {
        return this.timezone.gmtStandard === 1 ? -1 : 1
      },
      timeZoneList() {
        const list = TimezoneList.map((item, index) => {
          item.timezoneId = index + 1
          return item
        })
        return [
          {
            label: this.$t('dialog.customize'),
            value: 0xffff,
            timezoneId: 0xff,
          },
        ].concat(list)
      },
      datetimeString() {
        const { year, month, date, hours, minutes, seconds } = this.timezone
        // 使用新计算的时间格式化时间文本
        const padStart = num => {
          return num.toString().padStart(2, '0')
        }

        return `${year}-${padStart(month)}-${padStart(date)} ${padStart(hours)}:${padStart(minutes)}:${padStart(seconds)}`
      },
      customTimezone() {
        return !this.showTimezoneList || this.timezone.timezoneId === 0xff
      },
      selectedTimezone() {
        return this.timeZoneList.find(item => item.timezoneId === this.timezone.timezoneId)
      },
    },
    methods: {
      calDatetimeDiff(timezoneOffset = TimezoneOffset) {
        this.calcTimezoneOffset(timezoneOffset)
        // 当前时差与目标时差差值
        const _Date = new Date()
        const datetimeDiff = _Date.getTimezoneOffset() - timezoneOffset
        // 重新计算时间参数
        this.calcLocalDatetime(new Date(_Date.getTime() + datetimeDiff * 60 * 1000))
      },
      selectTimeZone(_val) {
        if (this.timezone.timezoneId !== 0) {
          // 选择内置时区，计算时差的小时、分钟差数
          this.calDatetimeDiff(this.selectedTimezone.value)
        }

        this.setHoursDiff()
      },
      setTimezoneId(timezoneOffset = TimezoneOffset) {
        const timezoneItem = this.timeZoneList.find(item => item.value === timezoneOffset)
        this.timezone.timezoneId = timezoneItem ? timezoneItem.timezoneId : 0xff
      },
      setHoursDiff() {
        this.hoursDiff = this.timezoneSign * this.timezone.hoursDiff * -1
      },
      calcTimezoneDiff() {
        const sign = this.hoursDiff < 0 ? 1 : -1
        const timezoneOffset = sign * (Math.abs(this.hoursDiff) * 60 + this.timezone.minutesDiff)
        this.calDatetimeDiff(timezoneOffset)
      },
      syncTimezone() {
        const timezoneOffset = new Date().getTimezoneOffset()
        this.setTimezoneId(timezoneOffset)
        this.calcTimezoneOffset(timezoneOffset)
        this.calcLocalDatetime()
        this.setHoursDiff()
      },
      getDatetime() {
        const { year, month, date, hours, minutes, seconds } = this.timezone
        return new Date(year, month - 1, date, hours, minutes, seconds)
      },
      startDatetimeTimer() {
        if (this.timer !== null) {
          clearInterval(this.timer)
        }
        this.timer = setInterval(() => {
          const datetime = this.getDatetime()
          const { seconds } = this.timezone
          datetime.setSeconds(seconds + 1)
          this.calcLocalDatetime(datetime)
          this.emitResult()
        }, 1000)
      },
      calcTimezoneOffset(offset = TimezoneOffset) {
        Object.assign(this.timezone, decodeTimezoneOffset(offset))
      },
      calcLocalDatetime(datetime = new Date()) {
        Object.assign(this.timezone, getLocalDateTime(datetime))
      },
      emitResult() {
        this.$nextTick(() => {
          this.$emit('update:modelValue', this.timezone)
        })
      },
      getTimezone() {
        return this.timezone
      },
    },
    watch: {
      modelValue: {
        deep: true,
        immediate: true,
        handler(data) {
          // 同步参数
          Object.assign(this.timezone, data)
          this.setHoursDiff()
          // 重置时间ID 0xFF为自定义，0为指定时区
          if (this.timezone.timezoneId !== 0xff) {
            const timezoneOffset = this.timezoneSign * this.timezone.hoursDiff * 60
            const timezone = this.timeZoneList.find(item => item.value === timezoneOffset)
            if (timezone) {
              this.timezone.timezoneId = timezone.timezoneId
            }
          }
        },
      },
    },
    beforeMount() {
      // 启动计时器
      this.startDatetimeTimer()
    },
    beforeUnmount() {
      clearInterval(this.timer)
    },
  }
</script>

<style lang="scss">
  .timezone-wrapper {
    .sync-timezone-container {
      .el-form-item__content {
        .sync-button {
          margin-left: 10px;
        }
      }
    }
  }
</style>
