@use '@/css/common.scss';

.el-cascader.bf-cascader {
  @extend .bf-component-size;

  .el-input {
    height: 100%;

    .el-input__wrapper {
      --el-input-bg-color: transparent;
      --el-input-border-radius: 0;
      --el-input-border-color: rgba(148, 204, 232, 1);
      --el-input-hover-border-color: rgba(148, 204, 232, 0.7);
      --el-input-clear-hover-color: rgba(255, 255, 255, 0.7);
      --el-input-focus-border-color: #fff;
      --el-input-text-color: #fff;
      --el-input-icon-color: #fff;

      box-shadow: 0 0 0 var(--bf-border-size) var(--el-input-border-color, var(--el-border-color)) inset;
    }

    &:hover .el-input__wrapper {
      box-shadow: 0 0 0 var(--bf-border-size) var(--el-input-hover-border-color) inset;
    }

    &.is-focus .el-input__wrapper {
      box-shadow: 0 0 0 var(--bf-border-size) var(--el-input-focus-border-color) inset;
    }
  }
}

.el-form-item.is-error .el-form-item__content .el-cascader__wrapper {
  box-shadow: 0 0 0 var(--bf-border-size) var(--el-color-danger) inset;

  &:hover {
    box-shadow: 0 0 0 var(--bf-border-size) var(--el-color-danger) inset;
  }
}

.el-popper.bf-cascader-popper {
  --el-popper-border-radius: 10px;
  --el-popper-bg-color: #012C4A;
  --el-cascader-menu-hover-bg-color: rgba(6, 121, 204, 0.5);
  --el-cascader-popper-border-color: #50D5FF;
  --el-cascader-item-hover-bg-color: #01375C;
  --el-cascader-item-in-active-path-bg-color: var(--el-cascader-item-hover-bg-color);

  background-color: var(--el-popper-bg-color);
  border: 2px solid transparent;
  border-radius: var(--el-popper-border-radius);
  background-image: linear-gradient(var(--el-popper-bg-color), var(--el-popper-bg-color)), linear-gradient(to bottom, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
  background-clip: padding-box, border-box;
  background-origin: border-box;
  background-color: transparent;

  .el-popper__arrow::before {
    background-color: var(--el-popper-bg-color);
    border: 2px solid var(--el-cascader-popper-border-color);
  }

  .el-cascader-panel {
    background-color: transparent;
    border: none;

    .el-cascader-menu {
      background-color: transparent;
      border-right: 1px solid var(--el-cascader-popper-border-color);

      &:last-child {
        border-right: none;
      }

      .el-cascader-node {
        margin: 0 10px;
        border-radius: 2px;

        &:hover {
          background-color: var(--el-cascader-item-hover-bg-color);
        }

        &.is-active {
          background-color: var(--el-cascader-menu-hover-bg-color);
        }

        &.in-active-path {
          background-color: var(--el-cascader-item-in-active-path-bg-color);
        }

        .el-cascader-node__label {
          color: #fff;
        }

        .el-cascader-node__postfix {
          color: #fff;
        }
      }
    }
  }
}

.el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
  background-color: var(--el-cascader-item-hover-bg-color);
}