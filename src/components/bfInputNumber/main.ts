import { defineComponent, h, computed, ref } from 'vue'
import { ElInputNumber } from 'element-plus'
import 'element-plus/es/components/input-number/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfInputNumber2',
  props: {
    modelValue: {
      type: [Number, null],
      default: null,
    },
    ...ElInputNumber.props,
  },
  emits: ['update:modelValue'],
  setup(props: InstanceType<typeof ElInputNumber>['$props'], { emit, slots, expose, attrs }) {
    const myModelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })

    // 构建传递给 ElCheckbox 的属性对象
    const finalProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-input-number', attrs.class].filter(Boolean).join(' '),
        modelValue: myModelValue.value,
        'onUpdate:modelValue': (val: number | null) => {
          myModelValue.value = val
        },
      }
    })

    // 向父组件暴露组件实例
    const vmRef = ref<InstanceType<typeof ElInputNumber>>()
    expose({
      instance: vmRef,
    })

    // 使用 h 函数渲染
    return () =>
      h(
        ElInputNumber,
        {
          ...(finalProps.value as InstanceType<typeof ElInputNumber>['$props']),
          ref: vmRef,
        },
        {
          // 先展开所有父组件传递的 slots
          ...slots,
          // 只有当父组件没有传递 'decrease-icon' slot 时，才使用默认实现
          ...(!slots['decrease-icon'] && {
            'decrease-icon': () => h('i', { class: 'bf-iconfont bfdx-jian text-white !text-[18px]' }),
          }),
          // 只有当父组件没有传递 'increase-icon' slot 时，才使用默认实现
          ...(!slots['increase-icon'] && {
            'increase-icon': () => h('i', { class: 'bf-iconfont bfdx-jia text-white !text-[18px]' }),
          }),
        }
      )
  },
})
