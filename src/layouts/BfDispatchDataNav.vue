<template>
  <div class="pl-[40px] pb-[47px] flex flex-col items-start justify-center gap-[13px]">
    <template v-for="navItem in navList" :key="navItem.name">
      <MenuBtn :nav-item="navItem" :is-active="isActive(navItem.name as string)" @click="handleMenuClick" />
    </template>
  </div>
</template>

<script setup lang="ts">
  import { RouteRecordRaw, useRouter, useRoute } from 'vue-router'
  import { computed } from 'vue'
  import MenuBtn from '@/components/common/MenuBtn.vue'

  const router = useRouter()
  const route = useRoute()
  // 获取 dispatch/DataApplication 的子路由
  const routes = computed(() => {
    const dispatchRoute = router.getRoutes().find(item => {
      return item.path === '/dispatch'
    })

    const dataApplicationRoute = dispatchRoute?.children?.find(child => {
      return child.name === 'DataApplication'
    })

    return dataApplicationRoute?.children || []
  })

  // 菜单配置对象参数
  const navList = computed<Array<RouteRecordRaw>>(() => {
    const list = routes.value
    list.sort((a, b) => {
      return a.meta?.navItemConfig?.value.order - b.meta?.navItemConfig?.value.order
    })

    return list
  })

  console.log('navList', navList.value)

  const isActive = (path: string) => {
    return route.path.includes(path)
  }

  const handleMenuClick = (navItem: RouteRecordRaw) => {
    // 直接跳转到对应的子路由
    const targetPath = `/dispatch/DataApplication/${String(navItem.name)}`

    router.push({
      path: targetPath,
      query: route.query, // 保持查询参数
    })
  }
</script>

<style scoped></style>
