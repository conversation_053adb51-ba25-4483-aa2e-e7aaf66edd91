<template>
  <main class="flex flex-col items-center no-wrap error-page">
    <section class="bg-blue-400 text-white w-full h-3/5 flex items-center justify-center error-code">404</section>
    <section
      class="shadow-2xl bg-white text-gray-800 w-11/12 md:w-1/2 p-10 transform -translate-y-16 rounded-xl flex-none flex flex-col gap-3 items-center justify-center no-wrap error-card"
    >
      <el-icon class="text-6xl text-gray-400">
        <Warning />
      </el-icon>
      <div class="text-center text-xl">
        {{ $t('error404.notFoundPage') }}
      </div>
      <div class="flex gap-3 mt-3">
        <el-button v-if="canGoBack" type="primary" icon="arrow-left" class="go-back-btn" @click="goBack">
          {{ $t('error404.goBack') }}
        </el-button>
        <el-button type="primary" icon="s-home" class="go-home-btn" @click="goHome">
          {{ $t('error404.goHome') }}
        </el-button>
      </div>
    </section>
  </main>
</template>

<script>
  export default {
    name: 'BfError404',
    methods: {
      goBack() {
        window.history.go(-1)
      },
      goHome() {
        const { origin, pathname } = window.location
        window.location.replace(origin + pathname)
      },
    },
    computed: {
      canGoBack() {
        return window.history.length > 1
      },
    },
  }
</script>

<style lang="scss">
  .error-page {
    width: 100vw;
    height: 100vh;

    .error-code {
      @media (orientation: landscape) {
        font-size: 16vw;
      }

      @media (orientation: portrait) {
        font-size: 16vh;
      }
    }
  }
</style>
