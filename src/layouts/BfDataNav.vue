<template>
  <div class="pl-[40px] pb-[47px] flex flex-col items-start justify-center gap-[13px]">
    <template v-for="navItem in navList" :key="navItem.name">
      <MenuBtn :nav-item="navItem" :is-active="isActive(navItem.name as string)" @click="handleMenuClick" />
    </template>
  </div>
</template>

<script setup lang="ts">
  import { RouteRecordRaw, useRouter, useRoute } from 'vue-router'
  import { computed, watch } from 'vue'
  import MenuBtn from '@/components/common/MenuBtn.vue'

  const router = useRouter()
  const route = useRoute()
  const routes =
    router.getRoutes().find(item => {
      return item.path === '/dataManage'
    })?.children || []

  // 菜单配置对象参数
  const navList = computed<Array<RouteRecordRaw>>(() => {
    const list = routes
    list.sort((a, b) => {
      return a.meta?.navItemConfig?.value.order - b.meta?.navItemConfig?.value.order
    })

    return list
  })

  const isActive = (path: string) => {
    return route.path.includes(path)
  }

  const handleMenuClick = navItem => {
    // 获取用户最后访问的子路由
    const lastChildPath = getLastVisitedChildPath(navItem.name)

    let targetPath: string
    if (lastChildPath) {
      targetPath = `/dataManage/${navItem.name}/${lastChildPath}`
    } else {
      targetPath = navItem.redirect || `/dataManage/${navItem.name}`
    }

    router.push({
      path: targetPath,
      query: route.query, // 保持查询参数
    })
  }

  // 内存对象存储最后访问的子路由
  const lastVisitedPaths: Record<string, string> = {}

  // 获取最后访问的子路由
  const getLastVisitedChildPath = (parentName: string): string | null => {
    return lastVisitedPaths[parentName] || null
  }

  // 保存当前访问的子路由
  const saveCurrentChildPath = () => {
    const currentRoute = route.matched[1] // 父路由
    const childRoute = route.matched[2] // 子路由

    if (currentRoute && childRoute && currentRoute.name && childRoute.name) {
      const parentName = String(currentRoute.name)
      const childName = String(childRoute.name)
      lastVisitedPaths[parentName] = childName
    }
  }

  // 监听路由变化，保存当前子路由
  watch(
    () => route.path,
    () => {
      saveCurrentChildPath()
    },
    { immediate: true }
  )
</script>

<style scoped></style>
