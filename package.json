{"name": "bfdx8000-system", "description": "A Vue.js project", "version": "3.6.10", "minServerVersion": "2.93.5", "repository": "*******************:bfdx/bf8100-web.git", "author": "linfulong <<EMAIL>>", "license": "MIT", "private": true, "type": "module", "engines": {"node": ">=20.x", "pnpm": ">=10.x"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "pbjs": "./pbjs.sh", "wf": "cd doc/wf && markdown-pdf BF8100-WF-infomation.md -o ../../build/BF8100-手台写频操作说明.pdf", "gateway": "cd doc/gateway && markdown-pdf gateway-operate-info.md -o ../../build/电话网关频操作说明.pdf", "build:md": "pnpm run wf && pnpm run gateway", "model": "node ./generateModelInfo.cjs", "check": "tsc --incremental --noEmit", "lint": "eslint --cache .", "format": "prettier --write --cache .", "format-check": "prettier --check --cache .", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{js,ts,vue,json}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@bufbuild/protobuf": "^1.10.1", "@connectrpc/connect": "^1.6.1", "@connectrpc/connect-web": "^1.6.1", "@element-plus/icons-vue": "^2.3.2", "@mdi/font": "^7.4.47", "@turf/turf": "^7.2.0", "@vueuse/core": "^13.7.0", "aes-js": "^3.1.2", "async-validator": "^4.2.5", "base64-js": "^1.5.1", "bootstrap": "^4.6.2", "compare-versions": "^6.1.1", "crypto-js": "^4.2.0", "datatables.net-bs4": "^2.3.2", "datatables.net-buttons-bs4": "^3.2.4", "datatables.net-scroller-bs4": "^2.4.3", "datatables.net-staterestore-bs4": "^1.4.1", "datatables.net-vue3": "^3.0.4", "dayjs": "^1.11.13", "detect-browser": "^5.3.0", "element-plus": "^2.10.7", "jquery": "^3.7.1", "jquery-ui": "~1.13.3", "jquery.fancytree": "^2.38.5", "lodash": "^4.17.21", "long": "^4.0.0", "maplibre-gl": "^5.6.2", "pako": "^1.0.11", "protobufjs": "^6.11.4", "qwebchannel": "~5.9.0", "screenfull": "^5.2.0", "ui-contextmenu": "^1.18.1", "uuid": "^10.0.0", "vue": "^3.5.18", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1", "vue-virtual-scroller": "2.0.0-beta.8", "vxe-table": "^4.16.0", "websocket-nats": "^0.3.3", "xe-utils": "^3.7.8", "xlsx": "^0.18.5", "ypubsub": "^1.0.15"}, "devDependencies": {"@bufbuild/buf": "1.50.0", "@bufbuild/protoc-gen-es": "^1.10.1", "@connectrpc/protoc-gen-connect-es": "^1.6.1", "@eslint/js": "^9.33.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.6", "@semantic-release/npm": "^12.0.2", "@semantic-release/release-notes-generator": "^14.0.3", "@tailwindcss/vite": "^4.1.12", "@types/jquery": "^3.5.32", "@types/jquery.fancytree": "^0.0.11", "@types/jqueryui": "^1.12.24", "@types/node": "^24.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-standard": "^9.0.1", "consola": "^3.4.2", "conventional-changelog-conventionalcommits": "^9.1.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-vue": "^9.33.0", "globals": "^15.15.0", "lint-staged": "^16.1.5", "postcss-pxtorem": "^6.1.0", "prettier": "^3.6.2", "sass-embedded": "^1.90.0", "semantic-release": "^24.2.7", "setimmediate": "^1.0.5", "simple-git-hooks": "^2.13.1", "tailwindcss": "^4.1.12", "terser": "^5.43.1", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-node-polyfills": "^0.24.0", "vue-template-compiler": "^2.7.16"}}